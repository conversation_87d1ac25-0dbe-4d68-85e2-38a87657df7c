const express = require('express');
const router = express.Router();

const { DatabaseOperations } = require('../config/database');
const { parseEncryptedRequest, sendEncryptedResponse } = require('../middleware/auth');
const { 
    validateActivationCode, 
    validateDeviceId, 
    calculateRemainingDays,
    isExpired 
} = require('../utils/activation');

/**
 * 用户激活接口
 * POST /api/activate
 * 
 * 请求格式 (加密):
 * {
 *   "encrypted": "base64_encrypted_data"
 * }
 * 
 * 解密后的数据:
 * {
 *   "action": "activate",
 *   "deviceId": "AND-12345678",
 *   "activationCode": "ABCD-EFGH-IJKL-MNOP",
 *   "platform": "android"
 * }
 */
router.post('/activate', parseEncryptedRequest, async (req, res) => {
    try {
        const { deviceId, activationCode, platform, action } = req.decryptedData;

        console.log(`Activation request: deviceId=${deviceId}, code=${activationCode}, platform=${platform}`);

        // 验证请求格式
        if (action !== 'activate') {
            return sendEncryptedResponse(res, {
                success: false,
                message: "无效的请求类型"
            }, 400);
        }

        // 验证激活码格式
        if (!validateActivationCode(activationCode)) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "激活码格式错误"
            }, 400);
        }

        // 验证设备ID格式
        if (!validateDeviceId(deviceId)) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "设备ID格式错误"
            }, 400);
        }

        // 查询激活码
        const codeData = await DatabaseOperations.getActivationCode(activationCode);
        if (!codeData) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "激活码不存在"
            });
        }

        // 检查激活码是否过期
        if (isExpired(codeData.expire_date)) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "激活码已过期"
            });
        }

        // 检查激活码是否已绑定设备
        if (!codeData.device_id) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "激活码未绑定设备，请联系管理员"
            });
        }

        // 验证设备ID是否匹配
        if (codeData.device_id !== deviceId) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "设备ID不匹配，此激活码已绑定其他设备"
            });
        }

        // 更新激活时间和最后验证时间
        await DatabaseOperations.updateActivationCode(activationCode, {
            activated_at: new Date(),
            last_verify_at: new Date()
        });

        // 计算剩余天数
        const expireDays = calculateRemainingDays(codeData.expire_date);

        // 返回成功响应
        sendEncryptedResponse(res, {
            success: true,
            message: "激活成功",
            expireDays: Math.max(0, expireDays),
            userName: codeData.user_name
        });

        console.log(`Activation successful: ${activationCode} -> ${deviceId}`);

    } catch (error) {
        console.error('Activation error:', error);
        sendEncryptedResponse(res, {
            success: false,
            message: "服务器错误，请稍后重试"
        }, 500);
    }
});

/**
 * 用户验证接口
 * POST /api/verify
 * 
 * 请求格式 (加密):
 * {
 *   "encrypted": "base64_encrypted_data"
 * }
 * 
 * 解密后的数据:
 * {
 *   "action": "verify",
 *   "deviceId": "AND-12345678",
 *   "activationCode": "ABCD-EFGH-IJKL-MNOP"
 * }
 */
router.post('/verify', parseEncryptedRequest, async (req, res) => {
    try {
        const { deviceId, activationCode, action } = req.decryptedData;

        console.log(`Verification request: deviceId=${deviceId}, code=${activationCode}`);

        // 验证请求格式
        if (action !== 'verify') {
            return sendEncryptedResponse(res, {
                success: false,
                message: "无效的请求类型"
            }, 400);
        }

        // 验证激活码格式
        if (!validateActivationCode(activationCode)) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "激活码格式错误"
            }, 400);
        }

        // 验证设备ID格式
        if (!validateDeviceId(deviceId)) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "设备ID格式错误"
            }, 400);
        }

        // 查询激活码
        const codeData = await DatabaseOperations.getActivationCode(activationCode);
        if (!codeData) {
            return sendEncryptedResponse(res, {
                success: false,
                message: "激活码不存在"
            });
        }

        // 验证设备ID是否匹配
        if (codeData.device_id !== deviceId) {
            return sendEncryptedResponse(res, {
                success: true,
                isActive: false,
                message: "设备ID不匹配"
            });
        }

        // 计算剩余天数
        const expireDays = calculateRemainingDays(codeData.expire_date);
        const isActiveStatus = expireDays > 0 && codeData.is_active;

        // 更新最后验证时间
        if (isActiveStatus) {
            await DatabaseOperations.updateActivationCode(activationCode, {
                last_verify_at: new Date()
            });
        }

        // 返回验证结果
        if (isActiveStatus) {
            sendEncryptedResponse(res, {
                success: true,
                isActive: true,
                expireDays: expireDays,
                message: "激活状态有效"
            });
        } else {
            const message = expireDays <= 0 ? "激活已过期" : "激活状态无效";
            sendEncryptedResponse(res, {
                success: true,
                isActive: false,
                message: message
            });
        }

        console.log(`Verification result: ${activationCode} -> ${isActiveStatus ? 'valid' : 'invalid'}`);

    } catch (error) {
        console.error('Verification error:', error);
        sendEncryptedResponse(res, {
            success: false,
            message: "服务器错误，请稍后重试"
        }, 500);
    }
});

module.exports = router;
