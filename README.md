# 激活码验证系统 - 生产版本

这是一个完整的激活码验证系统，包含后端API和Web管理界面，支持AES-128-CBC加密通信，提供用户激活验证和完整的管理后台功能。

## ✨ 主要特性

- 🔐 **AES-128-CBC加密通信** - 保护数据传输安全
- 🎯 **设备绑定机制** - 激活码与设备ID绑定
- 🌐 **Web管理后台** - 完整的可视化管理界面
- 🛡️ **安全防护** - 频率限制、权限验证、输入校验
- 📊 **统计报表** - 实时数据统计和可视化
- 🚀 **生产就绪** - Docker支持、PM2管理、Nginx配置
- 📱 **响应式设计** - 支持桌面和移动设备访问

## 📋 项目概述

### 系统特性
- **加密通信**: AES-128-CBC加密保护数据传输
- **设备绑定**: 激活码与设备ID绑定机制
- **权限管理**: 管理员和用户分离的API接口
- **数据持久化**: MySQL数据库存储
- **安全防护**: 频率限制、输入验证、错误处理
- **监控日志**: 完整的操作日志和统计信息

### 技术栈
- **后端框架**: Node.js + Express
- **数据库**: MySQL 8.0+
- **加密**: AES-128-CBC (Node.js crypto)
- **Windows集成**: PM2 + Windows服务 + IIS
- **安全**: Helmet, CORS, Rate Limiting, 防火墙配置

## 🚀 Windows Server 部署

### 一键部署

```batch
# 1. 将项目文件复制到服务器
# 推荐路径: C:\inetpub\activation-server\

# 2. 以管理员身份运行部署脚本
deploy-windows.bat

# 3. 按菜单依次执行:
# 1 → 检查环境依赖
# 2 → 安装项目依赖
# 3 → 配置环境变量
# 4 → 初始化数据库
# 6 → 启动服务(PM2)

# 4. 配置防火墙
configure-firewall.bat
```

### 访问系统

部署完成后，您可以访问：

- **API服务**: http://localhost:3001
- **管理后台**: http://localhost:3001/admin
- **健康检查**: http://localhost:3001/health

管理员登录：
- 使用管理员令牌登录（不显示具体令牌）

> 📖 详细部署说明请参考：
> - 快速开始: [WINDOWS-QUICKSTART.md](WINDOWS-QUICKSTART.md)
> - 详细指南: [WINDOWS-DEPLOYMENT.md](WINDOWS-DEPLOYMENT.md)

## 📊 API接口文档

### 用户接口

#### 激活接口
```http
POST /api/activate
Content-Type: application/json
X-Encryption: AES-128-CBC

{
  "encrypted": "base64_encrypted_data"
}
```

解密后的请求数据：
```json
{
  "action": "activate",
  "deviceId": "AND-12345678",
  "activationCode": "ABCD-EFGH-IJKL-MNOP",
  "platform": "android"
}
```

#### 验证接口
```http
POST /api/verify
Content-Type: application/json
X-Encryption: AES-128-CBC

{
  "encrypted": "base64_encrypted_data"
}
```

解密后的请求数据：
```json
{
  "action": "verify",
  "deviceId": "AND-12345678",
  "activationCode": "ABCD-EFGH-IJKL-MNOP"
}
```

### 管理员接口

#### 生成激活码
```http
POST /admin/generate-code
Content-Type: application/json

{
  "userName": "用户名",
  "expireDays": 30,
  "adminToken": "admin_secret_token"
}
```

#### 绑定设备
```http
POST /admin/bind-device
Content-Type: application/json

{
  "activationCode": "ABCD-EFGH-IJKL-MNOP",
  "deviceId": "AND-12345678",
  "adminToken": "admin_secret_token"
}
```

#### 查询激活码状态
```http
GET /admin/activation-status/ABCD-EFGH-IJKL-MNOP?adminToken=admin_secret_token
```

#### 获取激活码列表
```http
GET /admin/activation-list?adminToken=admin_secret_token&page=1&limit=20&status=all
```

#### 解绑设备
```http
POST /admin/unbind-device
Content-Type: application/json

{
  "activationCode": "ABCD-EFGH-IJKL-MNOP",
  "adminToken": "admin_secret_token"
}
```

#### 延长有效期
```http
POST /admin/extend-expire
Content-Type: application/json

{
  "activationCode": "ABCD-EFGH-IJKL-MNOP",
  "expireDays": 30,
  "adminToken": "admin_secret_token"
}
```

#### 获取统计信息
```http
GET /admin/statistics?adminToken=admin_secret_token
```

### 系统接口

#### 健康检查
```http
GET /health
```

#### API信息
```http
GET /api/info
```

## 🔐 加密说明

### 加密配置
```javascript
const FIXED_ENCRYPTION_KEY = "XwzcActivation24"; // 16字节
const FIXED_ENCRYPTION_IV = "InitVector123456";  // 16字节
```

### 加密示例 (JavaScript)
```javascript
const crypto = require('crypto');

function aesEncrypt(data) {
    const cipher = crypto.createCipheriv('aes-128-cbc', 'XwzcActivation24', 'InitVector123456');
    let encrypted = cipher.update(data, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
}

function aesDecrypt(encryptedData) {
    const decipher = crypto.createDecipheriv('aes-128-cbc', 'XwzcActivation24', 'InitVector123456');
    let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}
```

## 🗄️ 数据库结构

### 激活码表 (activation_codes)
| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键ID |
| code | VARCHAR(19) | 激活码 |
| device_id | VARCHAR(50) | 绑定的设备ID |
| user_name | VARCHAR(100) | 用户名 |
| expire_date | DATE | 过期日期 |
| is_active | BOOLEAN | 是否激活 |
| created_at | TIMESTAMP | 创建时间 |
| bound_at | TIMESTAMP | 绑定时间 |
| activated_at | TIMESTAMP | 激活时间 |
| last_verify_at | TIMESTAMP | 最后验证时间 |

## 🧪 测试

### 运行测试
```bash
# 确保服务器正在运行
npm start

# 在另一个终端运行测试
npm test
```

### 测试内容
- 健康检查
- 生成激活码
- 绑定设备
- 用户激活
- 用户验证
- 查询状态
- 获取统计信息

## 📦 部署

### 使用PM2部署
```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start server.js --name activation-server

# 设置开机自启
pm2 startup
pm2 save
```

### 使用Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

### Nginx反向代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 🔧 配置说明

### 环境变量
| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| DB_HOST | localhost | 数据库主机 |
| DB_USER | root | 数据库用户名 |
| DB_PASSWORD | password | 数据库密码 |
| DB_NAME | activation_system | 数据库名称 |
| PORT | 3001 | 服务器端口 |
| NODE_ENV | development | 运行环境 |
| ADMIN_TOKEN | admin_secret_token | 管理员令牌 |

### 安全建议
1. **更改默认管理员令牌**
2. **使用HTTPS**（生产环境）
3. **限制数据库访问权限**
4. **定期备份数据库**
5. **监控服务器日志**

## 📝 维护

### 日志查看
```bash
# PM2日志
pm2 logs activation-server

# 应用日志
tail -f logs/app.log
```

### 数据库维护
```sql
-- 清理过期激活码
CALL CleanupExpiredCodes();

-- 查看统计信息
CALL GetActivationStatistics();
```

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交 Issue 或联系开发团队。
