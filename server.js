const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { RateLimiterMemory } = require('rate-limiter-flexible');
require('dotenv').config();

// 导入配置和工具
const { DatabaseOperations } = require('./config/database');
const { validateEncryptionConfig } = require('./utils/encryption');

// 导入中间件
const { 
    requestLogger, 
    errorHandler, 
    corsConfig 
} = require('./middleware/auth');

// 导入路由
const userRoutes = require('./routes/user');
const adminRoutes = require('./routes/admin');
const webRoutes = require('./routes/web');

// 创建Express应用
const app = express();

// =====================================================
// 基础配置
// =====================================================
const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

console.log(`Starting server in ${NODE_ENV} mode...`);

// =====================================================
// 安全中间件
// =====================================================
app.use(helmet({
    contentSecurityPolicy: false, // 根据需要调整
    crossOriginEmbedderPolicy: false
}));

// CORS配置
const corsOrigin = process.env.CORS_ORIGIN || '*';
app.use(cors({
    origin: corsOrigin === '*' ? '*' : corsOrigin.split(','),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Admin-Token', 'X-Encryption'],
    credentials: false
}));

// =====================================================
// 请求解析中间件
// =====================================================
app.use(express.json({ 
    limit: '10mb',
    strict: true
}));

app.use(express.urlencoded({
    extended: true,
    limit: '10mb'
}));

// =====================================================
// 静态文件服务
// =====================================================
app.use('/admin', express.static('public/admin'));
app.use('/assets', express.static('public/assets'));

// =====================================================
// 频率限制
// =====================================================
const rateLimitWindow = parseInt(process.env.RATE_LIMIT_WINDOW) || 60;
const rateLimitMax = parseInt(process.env.RATE_LIMIT_MAX) || 100;

const rateLimiter = new RateLimiterMemory({
    keyGenerator: (req) => req.ip,
    points: rateLimitMax, // 每个IP每分钟的请求数
    duration: rateLimitWindow, // 时间窗口（秒）
    blockDuration: rateLimitWindow, // 被阻止时间（秒）
});

app.use(async (req, res, next) => {
    try {
        await rateLimiter.consume(req.ip);
        next();
    } catch (rejRes) {
        console.log(`Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({ 
            success: false,
            error: 'Too Many Requests',
            message: '请求过于频繁，请稍后再试'
        });
    }
});

// =====================================================
// 请求日志中间件
// =====================================================
app.use(requestLogger);

// =====================================================
// 健康检查接口
// =====================================================
app.get('/health', async (req, res) => {
    try {
        // 检查数据库连接
        const dbStatus = await DatabaseOperations.testConnection();
        
        // 检查加密配置
        const encryptionStatus = validateEncryptionConfig();
        
        const healthStatus = {
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            database: dbStatus ? 'connected' : 'disconnected',
            encryption: encryptionStatus ? 'ok' : 'error',
            memory: process.memoryUsage(),
            version: process.version,
            environment: NODE_ENV
        };
        
        const httpStatus = dbStatus && encryptionStatus ? 200 : 503;
        res.status(httpStatus).json(healthStatus);
        
    } catch (error) {
        console.error('Health check error:', error);
        res.status(503).json({
            status: 'error',
            message: 'Health check failed',
            timestamp: new Date().toISOString()
        });
    }
});

// =====================================================
// API信息接口
// =====================================================
app.get('/api/info', (req, res) => {
    res.json({
        name: 'Activation Code Verification System',
        version: '1.0.0',
        description: '激活码验证系统API',
        endpoints: {
            user: {
                activate: 'POST /api/activate',
                verify: 'POST /api/verify'
            },
            admin: {
                generateCode: 'POST /admin/generate-code',
                bindDevice: 'POST /admin/bind-device',
                activationStatus: 'GET /admin/activation-status/:code',
                activationList: 'GET /admin/activation-list',
                unbindDevice: 'POST /admin/unbind-device',
                extendExpire: 'POST /admin/extend-expire',
                statistics: 'GET /admin/statistics'
            },
            system: {
                health: 'GET /health',
                info: 'GET /api/info'
            }
        },
        encryption: 'AES-128-CBC',
        timestamp: new Date().toISOString()
    });
});

// =====================================================
// 注册路由
// =====================================================
app.use('/api', userRoutes);

// 管理员API路由 (需要在静态文件之前)
app.use('/admin', adminRoutes);

// Web管理后台路由（处理页面路由）
app.use('/admin', webRoutes);

// =====================================================
// 404处理
// =====================================================
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'API endpoint not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
    });
});

// =====================================================
// 错误处理中间件
// =====================================================
app.use(errorHandler);

// =====================================================
// 启动服务器
// =====================================================
async function startServer() {
    try {
        // 验证加密配置
        if (!validateEncryptionConfig()) {
            throw new Error('Encryption configuration validation failed');
        }
        console.log('✓ Encryption configuration validated');

        // 测试数据库连接
        const dbConnected = await DatabaseOperations.testConnection();
        if (!dbConnected) {
            console.warn('⚠ Database connection failed, but server will start anyway');
        } else {
            console.log('✓ Database connection successful');
        }

        // 启动HTTP服务器
        const server = app.listen(PORT, '0.0.0.0', () => {
            console.log(`
╔══════════════════════════════════════════════════════════════╗
║                 Activation Server Started                    ║
╠══════════════════════════════════════════════════════════════╣
║ Port: ${PORT.toString().padEnd(53)} ║
║ Environment: ${NODE_ENV.padEnd(47)} ║
║ Time: ${new Date().toISOString().padEnd(47)} ║
║                                                              ║
║ API Endpoints:                                               ║
║ • Health Check: GET /health                                  ║
║ • API Info: GET /api/info                                    ║
║ • User Activate: POST /api/activate                          ║
║ • User Verify: POST /api/verify                              ║
║ • Admin APIs: /admin/*                                       ║
╚══════════════════════════════════════════════════════════════╝
            `);
        });

        // 优雅关闭处理
        process.on('SIGTERM', () => {
            console.log('SIGTERM received, shutting down gracefully...');
            server.close(() => {
                console.log('Server closed');
                process.exit(0);
            });
        });

        process.on('SIGINT', () => {
            console.log('SIGINT received, shutting down gracefully...');
            server.close(() => {
                console.log('Server closed');
                process.exit(0);
            });
        });

    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

// 启动服务器
startServer();

module.exports = app;
