@echo off
setlocal enabledelayedexpansion

:: 激活码验证系统 - Windows Server 部署脚本
:: 支持传统部署和PM2管理

echo.
echo ========================================
echo   激活码验证系统 - Windows Server 部署
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 设置颜色
for /f %%A in ('"prompt $H &echo on &for %%B in (1) do rem"') do set BS=%%A

:: 显示菜单
:menu
cls
echo.
echo ========================================
echo   激活码验证系统 - Windows Server 部署
echo ========================================
echo.
echo 请选择部署方式:
echo.
echo 1. 检查环境依赖
echo 2. 安装项目依赖
echo 3. 配置环境变量
echo 4. 初始化数据库
echo 5. 启动服务 (开发模式)
echo 6. 启动服务 (PM2管理)
echo 7. 停止服务
echo 8. 查看服务状态
echo 9. 查看日志
echo 0. 退出
echo.
set /p choice="请输入选项 (0-9): "

if "%choice%"=="1" goto check_env
if "%choice%"=="2" goto install_deps
if "%choice%"=="3" goto config_env
if "%choice%"=="4" goto init_db
if "%choice%"=="5" goto start_dev
if "%choice%"=="6" goto start_pm2
if "%choice%"=="7" goto stop_service
if "%choice%"=="8" goto check_status
if "%choice%"=="9" goto show_logs
if "%choice%"=="0" goto exit
goto menu

:: 检查环境依赖
:check_env
echo.
echo [信息] 检查环境依赖...
echo.

:: 检查Node.js
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] Node.js 未安装
    echo 请从 https://nodejs.org 下载并安装 Node.js 16.0+
    goto menu_pause
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [成功] Node.js 已安装: !NODE_VERSION!
)

:: 检查npm
npm --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] npm 未安装
    goto menu_pause
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo [成功] npm 已安装: !NPM_VERSION!
)

:: 检查MySQL (可选)
mysql --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] MySQL 未安装或未添加到PATH
    echo 请确保MySQL已安装并可访问
) else (
    echo [成功] MySQL 已安装
)

:: 检查PM2 (可选)
pm2 --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] PM2 未安装，可选择安装: npm install -g pm2
) else (
    for /f "tokens=*" %%i in ('pm2 --version') do set PM2_VERSION=%%i
    echo [成功] PM2 已安装: !PM2_VERSION!
)

echo.
echo 环境检查完成！
goto menu_pause

:: 安装项目依赖
:install_deps
echo.
echo [信息] 安装项目依赖...
echo.

if not exist package.json (
    echo [错误] package.json 文件不存在
    goto menu_pause
)

npm install
if %errorLevel% neq 0 (
    echo [错误] 依赖安装失败
    goto menu_pause
)

echo.
echo [成功] 依赖安装完成！
goto menu_pause

:: 配置环境变量
:config_env
echo.
echo [信息] 配置环境变量...
echo.

if not exist .env (
    if exist .env.example (
        copy .env.example .env >nul
        echo [成功] 已创建 .env 文件
    ) else (
        echo [错误] .env.example 文件不存在
        goto menu_pause
    )
) else (
    echo [信息] .env 文件已存在
)

echo.
echo 请编辑 .env 文件配置以下重要参数:
echo - DB_HOST: 数据库主机地址
echo - DB_USER: 数据库用户名
echo - DB_PASSWORD: 数据库密码
echo - ADMIN_TOKEN: 管理员令牌 (请设置强密码)
echo.
set /p edit_env="是否现在编辑 .env 文件? (y/n): "
if /i "%edit_env%"=="y" (
    notepad .env
)

goto menu_pause

:: 初始化数据库
:init_db
echo.
echo [信息] 初始化数据库...
echo.

if not exist database.sql (
    echo [错误] database.sql 文件不存在
    goto menu_pause
)

set /p db_host="请输入数据库主机 (默认: localhost): "
if "%db_host%"=="" set db_host=localhost

set /p db_user="请输入数据库用户名 (默认: root): "
if "%db_user%"=="" set db_user=root

set /p db_password="请输入数据库密码: "

echo.
echo [信息] 正在初始化数据库...
mysql -h %db_host% -u %db_user% -p%db_password% < database.sql
if %errorLevel% neq 0 (
    echo [错误] 数据库初始化失败
    goto menu_pause
)

echo [成功] 数据库初始化完成！
goto menu_pause

:: 启动服务 (开发模式)
:start_dev
echo.
echo [信息] 启动服务 (开发模式)...
echo.

:: 检查端口占用
netstat -an | findstr ":3001" >nul
if %errorLevel% equ 0 (
    echo [警告] 端口 3001 已被占用
    set /p kill_port="是否停止占用端口的进程? (y/n): "
    if /i "!kill_port!"=="y" (
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3001"') do (
            taskkill /PID %%a /F >nul 2>&1
        )
    )
)

if not exist logs mkdir logs

echo [信息] 启动服务器...
echo 按 Ctrl+C 停止服务
echo.
node server.js
goto menu_pause

:: 启动服务 (PM2管理)
:start_pm2
echo.
echo [信息] 使用 PM2 启动服务...
echo.

pm2 --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] PM2 未安装
    set /p install_pm2="是否安装 PM2? (y/n): "
    if /i "!install_pm2!"=="y" (
        npm install -g pm2
        if !errorLevel! neq 0 (
            echo [错误] PM2 安装失败
            goto menu_pause
        )
    ) else (
        goto menu_pause
    )
)

if not exist logs mkdir logs

:: 停止已存在的进程
pm2 stop activation-server >nul 2>&1
pm2 delete activation-server >nul 2>&1

:: 启动新进程
if exist ecosystem.config.js (
    pm2 start ecosystem.config.js --env production
) else (
    pm2 start server.js --name activation-server --env production
)

if %errorLevel% neq 0 (
    echo [错误] PM2 启动失败
    goto menu_pause
)

pm2 save >nul 2>&1
echo [成功] 服务已启动！
echo.
echo 管理命令:
echo - 查看状态: pm2 status
echo - 查看日志: pm2 logs activation-server
echo - 重启服务: pm2 restart activation-server
echo - 停止服务: pm2 stop activation-server
goto menu_pause

:: 停止服务
:stop_service
echo.
echo [信息] 停止服务...
echo.

:: 停止PM2进程
pm2 list 2>nul | findstr "activation-server" >nul
if %errorLevel% equ 0 (
    pm2 stop activation-server
    pm2 delete activation-server
    echo [成功] PM2 服务已停止
) else (
    echo [信息] 未找到 PM2 进程
)

:: 停止端口占用的进程
for /f "tokens=5" %%a in ('netstat -ano 2^>nul ^| findstr ":3001"') do (
    taskkill /PID %%a /F >nul 2>&1
    if !errorLevel! equ 0 echo [成功] 已停止端口 3001 的进程
)

goto menu_pause

:: 查看服务状态
:check_status
echo.
echo [信息] 检查服务状态...
echo.

:: 检查端口占用
netstat -an | findstr ":3001" >nul
if %errorLevel% equ 0 (
    echo [成功] 端口 3001 正在监听
) else (
    echo [警告] 端口 3001 未被占用
)

:: 检查PM2状态
pm2 list 2>nul | findstr "activation-server" >nul
if %errorLevel% equ 0 (
    echo [信息] PM2 进程状态:
    pm2 status activation-server
) else (
    echo [信息] 未找到 PM2 进程
)

:: 检查HTTP响应
echo.
echo [信息] 检查HTTP响应...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001/health' -TimeoutSec 5; Write-Host '[成功] 服务响应正常'; $response.Content } catch { Write-Host '[错误] 服务无响应' }"

goto menu_pause

:: 查看日志
:show_logs
echo.
echo [信息] 查看日志...
echo.

echo 选择日志类型:
echo 1. PM2 日志
echo 2. 应用日志文件
echo 3. 返回主菜单
echo.
set /p log_choice="请选择 (1-3): "

if "%log_choice%"=="1" (
    pm2 logs activation-server --lines 50
) else if "%log_choice%"=="2" (
    if exist logs\app.log (
        type logs\app.log
    ) else (
        echo [警告] 日志文件不存在
    )
) else if "%log_choice%"=="3" (
    goto menu
)

goto menu_pause

:: 菜单暂停
:menu_pause
echo.
pause
goto menu

:: 退出
:exit
echo.
echo 感谢使用激活码验证系统部署脚本！
echo.
pause
exit /b 0
