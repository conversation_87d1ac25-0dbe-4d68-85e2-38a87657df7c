const crypto = require('crypto');

/**
 * 生成激活码
 * 格式: XXXX-XXXX-XXXX-XXXX (16个字符，每4个字符用-分隔)
 * 字符集: A-Z, 0-9
 * @returns {string} - 生成的激活码
 */
function generateActivationCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    
    for (let i = 0; i < 16; i++) {
        if (i > 0 && i % 4 === 0) {
            result += '-';
        }
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
}

/**
 * 验证激活码格式
 * @param {string} code - 要验证的激活码
 * @returns {boolean} - 格式是否正确
 */
function validateActivationCode(code) {
    if (!code || typeof code !== 'string') {
        return false;
    }
    
    // 正则表达式: 4个字符-4个字符-4个字符-4个字符
    const pattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
    return pattern.test(code);
}

/**
 * 验证设备ID格式
 * 格式: AND-XXXXXXXX (AND-开头，后跟8位十六进制字符)
 * @param {string} deviceId - 要验证的设备ID
 * @returns {boolean} - 格式是否正确
 */
function validateDeviceId(deviceId) {
    if (!deviceId || typeof deviceId !== 'string') {
        return false;
    }
    
    // 正则表达式: AND-开头，后跟8位十六进制字符
    const pattern = /^AND-[A-F0-9]{8}$/;
    return pattern.test(deviceId);
}

/**
 * 生成设备ID (用于测试)
 * @returns {string} - 生成的设备ID
 */
function generateDeviceId() {
    const hexChars = 'ABCDEF0123456789';
    let deviceId = 'AND-';
    
    for (let i = 0; i < 8; i++) {
        deviceId += hexChars.charAt(Math.floor(Math.random() * hexChars.length));
    }
    
    return deviceId;
}

/**
 * 计算过期日期
 * @param {number} days - 有效天数
 * @returns {string} - 过期日期 (YYYY-MM-DD格式)
 */
function calculateExpireDate(days) {
    const expireDate = new Date();
    expireDate.setDate(expireDate.getDate() + days);
    return expireDate.toISOString().split('T')[0];
}

/**
 * 计算剩余天数
 * @param {string|Date} expireDate - 过期日期
 * @returns {number} - 剩余天数 (负数表示已过期)
 */
function calculateRemainingDays(expireDate) {
    const expire = new Date(expireDate);
    const now = new Date();
    const diffTime = expire - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

/**
 * 检查激活码是否过期
 * @param {string|Date} expireDate - 过期日期
 * @returns {boolean} - 是否已过期
 */
function isExpired(expireDate) {
    return calculateRemainingDays(expireDate) <= 0;
}

/**
 * 验证用户名格式
 * @param {string} userName - 用户名
 * @returns {boolean} - 格式是否正确
 */
function validateUserName(userName) {
    if (!userName || typeof userName !== 'string') {
        return false;
    }
    
    // 用户名长度限制: 1-100个字符
    return userName.length >= 1 && userName.length <= 100;
}

/**
 * 验证有效期天数
 * @param {number} days - 天数
 * @returns {boolean} - 是否有效
 */
function validateExpireDays(days) {
    return Number.isInteger(days) && days > 0 && days <= 3650; // 最多10年
}

/**
 * 格式化激活码状态
 * @param {Object} codeData - 激活码数据
 * @returns {string} - 状态描述
 */
function getActivationStatus(codeData) {
    if (!codeData) {
        return 'not_found';
    }
    
    if (isExpired(codeData.expire_date)) {
        return 'expired';
    }
    
    if (!codeData.device_id) {
        return 'unbound';
    }
    
    if (codeData.activated_at) {
        return 'activated';
    }
    
    return 'bound';
}

/**
 * 生成唯一激活码 (确保不重复)
 * @param {Function} checkExistsCallback - 检查激活码是否存在的回调函数
 * @param {number} maxAttempts - 最大尝试次数
 * @returns {Promise<string>} - 唯一的激活码
 */
async function generateUniqueActivationCode(checkExistsCallback, maxAttempts = 100) {
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        const code = generateActivationCode();
        
        try {
            const exists = await checkExistsCallback(code);
            if (!exists) {
                return code;
            }
        } catch (error) {
            console.error('Error checking code existence:', error);
            throw error;
        }
    }
    
    throw new Error('Failed to generate unique activation code after maximum attempts');
}

/**
 * 验证平台类型
 * @param {string} platform - 平台类型
 * @returns {boolean} - 是否有效
 */
function validatePlatform(platform) {
    const validPlatforms = ['android', 'ios', 'windows', 'mac', 'linux'];
    return validPlatforms.includes(platform?.toLowerCase());
}

module.exports = {
    generateActivationCode,
    validateActivationCode,
    validateDeviceId,
    generateDeviceId,
    calculateExpireDate,
    calculateRemainingDays,
    isExpired,
    validateUserName,
    validateExpireDays,
    getActivationStatus,
    generateUniqueActivationCode,
    validatePlatform
};
