@echo off
chcp 65001 >nul

:: 激活码验证系统 - Windows 防火墙配置脚本

echo.
echo ==========================================
echo   Windows 防火墙配置 - 激活码验证系统
echo ==========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

echo 请选择操作:
echo.
echo 1. 配置防火墙规则 (推荐)
echo 2. 移除防火墙规则
echo 3. 查看当前规则
echo 4. 测试端口连接
echo 0. 退出
echo.
set /p choice="请输入选项 (0-4): "

if "%choice%"=="1" goto configure_rules
if "%choice%"=="2" goto remove_rules
if "%choice%"=="3" goto show_rules
if "%choice%"=="4" goto test_ports
if "%choice%"=="0" goto exit
goto menu

:configure_rules
echo.
echo [信息] 配置防火墙规则...
echo.

:: 删除可能存在的旧规则
echo [信息] 清理旧规则...
netsh advfirewall firewall delete rule name="Activation Server - HTTP" >nul 2>&1
netsh advfirewall firewall delete rule name="Activation Server - HTTPS" >nul 2>&1
netsh advfirewall firewall delete rule name="Activation Server - API" >nul 2>&1
netsh advfirewall firewall delete rule name="Activation Server - Admin" >nul 2>&1

:: 添加入站规则 - API端口 (3001)
echo [信息] 添加 API 端口规则 (3001)...
netsh advfirewall firewall add rule ^
    name="Activation Server - API" ^
    dir=in ^
    action=allow ^
    protocol=TCP ^
    localport=3001 ^
    description="激活码验证系统 API 端口"

if %errorLevel% neq 0 (
    echo [错误] API 端口规则添加失败
    goto menu_pause
)

:: 添加入站规则 - HTTP (80)
echo [信息] 添加 HTTP 端口规则 (80)...
netsh advfirewall firewall add rule ^
    name="Activation Server - HTTP" ^
    dir=in ^
    action=allow ^
    protocol=TCP ^
    localport=80 ^
    description="激活码验证系统 HTTP 端口"

if %errorLevel% neq 0 (
    echo [警告] HTTP 端口规则添加失败 (可能已存在)
)

:: 添加入站规则 - HTTPS (443)
echo [信息] 添加 HTTPS 端口规则 (443)...
netsh advfirewall firewall add rule ^
    name="Activation Server - HTTPS" ^
    dir=in ^
    action=allow ^
    protocol=TCP ^
    localport=443 ^
    description="激活码验证系统 HTTPS 端口"

if %errorLevel% neq 0 (
    echo [警告] HTTPS 端口规则添加失败 (可能已存在)
)

:: 添加出站规则 - MySQL (3306) - 如果数据库在远程
set /p remote_db="数据库是否在远程服务器? (y/n): "
if /i "%remote_db%"=="y" (
    echo [信息] 添加 MySQL 出站规则 (3306)...
    netsh advfirewall firewall add rule ^
        name="Activation Server - MySQL Out" ^
        dir=out ^
        action=allow ^
        protocol=TCP ^
        remoteport=3306 ^
        description="激活码验证系统 MySQL 连接"
)

:: 配置高级安全设置
echo [信息] 配置高级安全设置...

:: 启用防火墙日志
netsh advfirewall set currentprofile logging filename %systemroot%\system32\LogFiles\Firewall\pfirewall.log
netsh advfirewall set currentprofile logging maxfilesize 4096
netsh advfirewall set currentprofile logging droppedconnections enable

:: 设置默认策略 (可选)
set /p strict_policy="是否启用严格策略 (阻止所有未明确允许的连接)? (y/n): "
if /i "%strict_policy%"=="y" (
    echo [信息] 启用严格防火墙策略...
    netsh advfirewall set allprofiles firewallpolicy blockinbound,blockoutbound
    
    :: 允许基本的出站连接
    netsh advfirewall firewall add rule name="Allow DNS Out" dir=out action=allow protocol=UDP remoteport=53
    netsh advfirewall firewall add rule name="Allow HTTP Out" dir=out action=allow protocol=TCP remoteport=80
    netsh advfirewall firewall add rule name="Allow HTTPS Out" dir=out action=allow protocol=TCP remoteport=443
    netsh advfirewall firewall add rule name="Allow NTP Out" dir=out action=allow protocol=UDP remoteport=123
)

echo.
echo [成功] 防火墙规则配置完成！
echo.
echo 已配置的规则:
echo - 入站 TCP 3001 (API 端口)
echo - 入站 TCP 80 (HTTP)
echo - 入站 TCP 443 (HTTPS)
if /i "%remote_db%"=="y" echo - 出站 TCP 3306 (MySQL)
echo.
goto menu_pause

:remove_rules
echo.
echo [信息] 移除防火墙规则...
echo.

:: 删除规则
echo [信息] 删除激活码验证系统相关规则...
netsh advfirewall firewall delete rule name="Activation Server - HTTP"
netsh advfirewall firewall delete rule name="Activation Server - HTTPS"
netsh advfirewall firewall delete rule name="Activation Server - API"
netsh advfirewall firewall delete rule name="Activation Server - MySQL Out"

echo [成功] 防火墙规则已移除！
goto menu_pause

:show_rules
echo.
echo [信息] 查看当前防火墙规则...
echo.

echo === 激活码验证系统相关规则 ===
netsh advfirewall firewall show rule name="Activation Server - HTTP" verbose
netsh advfirewall firewall show rule name="Activation Server - HTTPS" verbose
netsh advfirewall firewall show rule name="Activation Server - API" verbose
netsh advfirewall firewall show rule name="Activation Server - MySQL Out" verbose

echo.
echo === 防火墙状态 ===
netsh advfirewall show allprofiles state

echo.
echo === 端口监听状态 ===
netstat -an | findstr ":80 "
netstat -an | findstr ":443 "
netstat -an | findstr ":3001 "

goto menu_pause

:test_ports
echo.
echo [信息] 测试端口连接...
echo.

:: 测试本地端口
echo 测试本地端口连接:
echo.

echo [测试] 端口 3001 (API)...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 3001); $tcp.Close(); Write-Host '[成功] 端口 3001 可访问' } catch { Write-Host '[失败] 端口 3001 不可访问' }"

echo [测试] 端口 80 (HTTP)...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 80); $tcp.Close(); Write-Host '[成功] 端口 80 可访问' } catch { Write-Host '[失败] 端口 80 不可访问' }"

echo [测试] 端口 443 (HTTPS)...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('localhost', 443); $tcp.Close(); Write-Host '[成功] 端口 443 可访问' } catch { Write-Host '[失败] 端口 443 不可访问' }"

:: 测试 HTTP 响应
echo.
echo [测试] HTTP 响应...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001/health' -TimeoutSec 5; Write-Host '[成功] API 服务响应正常'; Write-Host $response.StatusCode } catch { Write-Host '[失败] API 服务无响应' }"

:: 测试外部访问 (如果有公网IP)
echo.
set /p test_external="是否测试外部访问? 需要公网IP (y/n): "
if /i "%test_external%"=="y" (
    set /p external_ip="请输入服务器公网IP: "
    if not "!external_ip!"=="" (
        echo [测试] 外部访问 !external_ip!:3001...
        powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://!external_ip!:3001/health' -TimeoutSec 10; Write-Host '[成功] 外部访问正常' } catch { Write-Host '[失败] 外部访问失败' }"
    )
)

goto menu_pause

:menu_pause
echo.
pause
goto menu

:menu
goto configure_firewall

:exit
echo.
echo 防火墙配置完成！
echo.
echo 重要提醒:
echo 1. 确保应用程序正在运行
echo 2. 检查路由器端口转发设置 (如果需要外网访问)
echo 3. 定期检查防火墙日志
echo 4. 生产环境建议启用严格策略
echo.
pause
exit /b 0
