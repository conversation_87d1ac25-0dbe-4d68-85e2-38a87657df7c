{"name": "activation-server", "version": "1.0.0", "description": "激活码验证服务器 - 生产版本", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "mysql -u root -p < database.sql", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop activation-server", "pm2:restart": "pm2 restart activation-server", "pm2:logs": "pm2 logs activation-server"}, "keywords": ["activation", "license", "verification", "express", "mysql"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.18.2", "helmet": "^7.0.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.0", "rate-limiter-flexible": "^2.4.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}