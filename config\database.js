const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'password',
    database: process.env.DB_NAME || 'activation_system',
    charset: 'utf8mb4',
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

// 数据库操作函数
class DatabaseOperations {
    
    // 查询激活码
    static async getActivationCode(code) {
        try {
            const [rows] = await pool.execute(
                'SELECT * FROM activation_codes WHERE code = ?',
                [code]
            );
            return rows[0] || null;
        } catch (error) {
            console.error('Database error in getActivationCode:', error);
            throw error;
        }
    }

    // 创建激活码
    static async createActivationCode(data) {
        try {
            const [result] = await pool.execute(
                'INSERT INTO activation_codes (code, user_name, expire_date) VALUES (?, ?, ?)',
                [data.code, data.userName, data.expireDate]
            );
            return result;
        } catch (error) {
            console.error('Database error in createActivationCode:', error);
            throw error;
        }
    }

    // 更新激活码
    static async updateActivationCode(code, updates) {
        try {
            const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
            const values = Object.values(updates);

            const [result] = await pool.execute(
                `UPDATE activation_codes SET ${fields} WHERE code = ?`,
                [...values, code]
            );
            return result;
        } catch (error) {
            console.error('Database error in updateActivationCode:', error);
            throw error;
        }
    }

    // 获取激活码列表
    static async getActivationList(page = 1, limit = 20, status = 'all') {
        try {
            // 确保参数为整数类型
            const pageNum = parseInt(page) || 1;
            const limitNum = parseInt(limit) || 20;
            const offset = (pageNum - 1) * limitNum;

            console.log(`getActivationList called with: page=${pageNum}, limit=${limitNum}, status=${status}, offset=${offset}`);

            let whereClause = '';
            let countWhereClause = '';

            if (status === 'bound') {
                whereClause = 'WHERE device_id IS NOT NULL';
                countWhereClause = 'WHERE device_id IS NOT NULL';
            } else if (status === 'unbound') {
                whereClause = 'WHERE device_id IS NULL';
                countWhereClause = 'WHERE device_id IS NULL';
            } else if (status === 'expired') {
                whereClause = 'WHERE expire_date < CURDATE()';
                countWhereClause = 'WHERE expire_date < CURDATE()';
            } else if (status === 'activated') {
                whereClause = 'WHERE activated_at IS NOT NULL';
                countWhereClause = 'WHERE activated_at IS NOT NULL';
            }

            // 使用字符串拼接避免参数问题
            const sql = `SELECT * FROM activation_codes ${whereClause} ORDER BY created_at DESC LIMIT ${limitNum} OFFSET ${offset}`;
            console.log('Executing SQL:', sql);

            const [rows] = await pool.execute(sql);

            const countSql = `SELECT COUNT(*) as total FROM activation_codes ${countWhereClause}`;
            console.log('Executing count SQL:', countSql);

            const [countRows] = await pool.execute(countSql);

            const result = {
                list: rows,
                total: countRows[0].total
            };

            console.log(`Query result: ${result.list.length} rows, total: ${result.total}`);
            return result;

        } catch (error) {
            console.error('Database error in getActivationList:', error);
            console.error('Error details:', {
                code: error.code,
                errno: error.errno,
                sqlMessage: error.sqlMessage,
                sql: error.sql
            });

            // 返回空结果而不是抛出错误
            return {
                list: [],
                total: 0
            };
        }
    }

    // 检查激活码是否存在
    static async checkCodeExists(code) {
        try {
            const [rows] = await pool.execute(
                'SELECT COUNT(*) as count FROM activation_codes WHERE code = ?',
                [code]
            );
            return rows[0].count > 0;
        } catch (error) {
            console.error('Database error in checkCodeExists:', error);
            throw error;
        }
    }

    // 获取统计信息
    static async getStatistics() {
        try {
            const [rows] = await pool.execute(`
                SELECT
                    COUNT(*) as total,
                    COUNT(device_id) as bound,
                    COUNT(activated_at) as activated,
                    COUNT(CASE WHEN expire_date < CURDATE() THEN 1 END) as expired
                FROM activation_codes
            `);
            return rows[0];
        } catch (error) {
            console.error('Database error in getStatistics:', error);
            throw error;
        }
    }

    // 删除激活码
    static async deleteActivationCode(code) {
        try {
            const [result] = await pool.execute(
                'DELETE FROM activation_codes WHERE code = ?',
                [code]
            );

            return result.affectedRows > 0;
        } catch (error) {
            console.error('Database error in deleteActivationCode:', error);
            throw error;
        }
    }

    // 清理过期的未激活码
    static async cleanupExpiredCodes() {
        try {
            const [result] = await pool.execute(
                'DELETE FROM activation_codes WHERE expire_date < CURDATE() AND activated_at IS NULL'
            );
            return result.affectedRows;
        } catch (error) {
            console.error('Database error in cleanupExpiredCodes:', error);
            throw error;
        }
    }

    // 测试数据库连接
    static async testConnection() {
        try {
            const connection = await pool.getConnection();
            await connection.ping();
            connection.release();
            console.log('Database connection successful');
            return true;
        } catch (error) {
            console.error('Database connection failed:', error);
            return false;
        }
    }
}

module.exports = {
    pool,
    DatabaseOperations,
    dbConfig
};
