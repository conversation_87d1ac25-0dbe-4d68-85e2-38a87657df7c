@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 激活码验证系统 - Windows 服务安装脚本

echo.
echo ==========================================
echo   激活码验证系统 - Windows 服务安装
echo ==========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 请以管理员身份运行此脚本
    pause
    exit /b 1
)

:: 获取当前目录
set CURRENT_DIR=%~dp0
set SERVICE_NAME=ActivationServer
set SERVICE_DISPLAY_NAME=Activation Code Verification Server
set SERVICE_DESCRIPTION=激活码验证系统服务

echo 当前目录: %CURRENT_DIR%
echo 服务名称: %SERVICE_NAME%
echo.

:: 显示菜单
:menu
echo 请选择操作:
echo.
echo 1. 安装服务
echo 2. 卸载服务
echo 3. 启动服务
echo 4. 停止服务
echo 5. 重启服务
echo 6. 查看服务状态
echo 7. 查看服务日志
echo 0. 退出
echo.
set /p choice="请输入选项 (0-7): "

if "%choice%"=="1" goto install_service
if "%choice%"=="2" goto uninstall_service
if "%choice%"=="3" goto start_service
if "%choice%"=="4" goto stop_service
if "%choice%"=="5" goto restart_service
if "%choice%"=="6" goto status_service
if "%choice%"=="7" goto show_logs
if "%choice%"=="0" goto exit
goto menu

:: 安装服务
:install_service
echo.
echo [信息] 安装 Windows 服务...
echo.

:: 检查 Node.js
node --version >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] Node.js 未安装，请先安装 Node.js
    goto menu_pause
)

:: 检查项目文件
if not exist server.js (
    echo [错误] server.js 文件不存在
    goto menu_pause
)

:: 检查 node-windows
npm list -g node-windows >nul 2>&1
if %errorLevel% neq 0 (
    echo [信息] 安装 node-windows...
    npm install -g node-windows
    if !errorLevel! neq 0 (
        echo [错误] node-windows 安装失败
        goto menu_pause
    )
)

:: 创建服务安装脚本
echo [信息] 创建服务安装脚本...
(
echo var Service = require('node-windows'^).Service;
echo.
echo // 创建服务对象
echo var svc = new Service^(^{
echo   name: '%SERVICE_NAME%',
echo   description: '%SERVICE_DESCRIPTION%',
echo   script: '%CURRENT_DIR%server.js',
echo   nodeOptions: [
echo     '--max-old-space-size=2048'
echo   ],
echo   env: [
echo     ^{
echo       name: 'NODE_ENV',
echo       value: 'production'
echo     ^},
echo     ^{
echo       name: 'PORT',
echo       value: '3001'
echo     ^}
echo   ]
echo ^}^);
echo.
echo // 监听安装事件
echo svc.on^('install', function^(^)^{
echo   console.log^('服务安装成功'^);
echo   svc.start^(^);
echo ^}^);
echo.
echo // 安装服务
echo svc.install^(^);
) > install-service.js

:: 执行安装
echo [信息] 执行服务安装...
node install-service.js
if %errorLevel% neq 0 (
    echo [错误] 服务安装失败
    goto cleanup_install
)

:: 等待服务安装完成
timeout /t 5 /nobreak >nul

:: 检查服务是否安装成功
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 服务安装失败
    goto cleanup_install
)

echo [成功] 服务安装完成！
echo.
echo 服务信息:
echo - 服务名称: %SERVICE_NAME%
echo - 显示名称: %SERVICE_DISPLAY_NAME%
echo - 服务状态: 已安装
echo.
echo 管理命令:
echo - 启动服务: net start %SERVICE_NAME%
echo - 停止服务: net stop %SERVICE_NAME%
echo - 查看状态: sc query %SERVICE_NAME%

:cleanup_install
if exist install-service.js del install-service.js
goto menu_pause

:: 卸载服务
:uninstall_service
echo.
echo [信息] 卸载 Windows 服务...
echo.

:: 检查服务是否存在
sc query %SERVICE_NAME% >nul 2>&1
if %errorLevel% neq 0 (
    echo [警告] 服务不存在
    goto menu_pause
)

:: 停止服务
echo [信息] 停止服务...
net stop %SERVICE_NAME% >nul 2>&1

:: 创建服务卸载脚本
echo [信息] 创建服务卸载脚本...
(
echo var Service = require('node-windows'^).Service;
echo.
echo // 创建服务对象
echo var svc = new Service^(^{
echo   name: '%SERVICE_NAME%',
echo   script: '%CURRENT_DIR%server.js'
echo ^}^);
echo.
echo // 监听卸载事件
echo svc.on^('uninstall', function^(^)^{
echo   console.log^('服务卸载成功'^);
echo ^}^);
echo.
echo // 卸载服务
echo svc.uninstall^(^);
) > uninstall-service.js

:: 执行卸载
echo [信息] 执行服务卸载...
node uninstall-service.js
if %errorLevel% neq 0 (
    echo [错误] 服务卸载失败
    goto cleanup_uninstall
)

:: 等待服务卸载完成
timeout /t 5 /nobreak >nul

echo [成功] 服务卸载完成！

:cleanup_uninstall
if exist uninstall-service.js del uninstall-service.js
goto menu_pause

:: 启动服务
:start_service
echo.
echo [信息] 启动服务...
echo.

net start %SERVICE_NAME%
if %errorLevel% neq 0 (
    echo [错误] 服务启动失败
) else (
    echo [成功] 服务启动成功！
)
goto menu_pause

:: 停止服务
:stop_service
echo.
echo [信息] 停止服务...
echo.

net stop %SERVICE_NAME%
if %errorLevel% neq 0 (
    echo [错误] 服务停止失败
) else (
    echo [成功] 服务停止成功！
)
goto menu_pause

:: 重启服务
:restart_service
echo.
echo [信息] 重启服务...
echo.

net stop %SERVICE_NAME%
timeout /t 2 /nobreak >nul
net start %SERVICE_NAME%
if %errorLevel% neq 0 (
    echo [错误] 服务重启失败
) else (
    echo [成功] 服务重启成功！
)
goto menu_pause

:: 查看服务状态
:status_service
echo.
echo [信息] 查看服务状态...
echo.

sc query %SERVICE_NAME%
echo.

:: 检查端口监听
netstat -an | findstr ":3001" >nul
if %errorLevel% equ 0 (
    echo [成功] 端口 3001 正在监听
) else (
    echo [警告] 端口 3001 未监听
)

:: 检查HTTP响应
echo.
echo [信息] 检查HTTP响应...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3001/health' -TimeoutSec 5; Write-Host '[成功] 服务响应正常'; $response.Content } catch { Write-Host '[错误] 服务无响应' }"

goto menu_pause

:: 查看服务日志
:show_logs
echo.
echo [信息] 查看服务日志...
echo.

:: Windows 事件日志
echo === Windows 事件日志 ===
powershell -Command "Get-EventLog -LogName Application -Source '%SERVICE_NAME%' -Newest 10 -ErrorAction SilentlyContinue | Format-Table TimeGenerated, EntryType, Message -Wrap"

echo.
echo === 应用程序日志 ===
if exist logs\app.log (
    powershell -Command "Get-Content logs\app.log -Tail 20"
) else (
    echo 应用程序日志文件不存在
)

goto menu_pause

:: 菜单暂停
:menu_pause
echo.
pause
goto menu

:: 退出
:exit
echo.
echo 感谢使用激活码验证系统服务管理脚本！
echo.
pause
exit /b 0
