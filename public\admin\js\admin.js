// 全局变量
let currentPage = 1;
let currentFilter = 'all';
const ADMIN_TOKEN = localStorage.getItem('adminToken') || 'cnmcnm66';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
function initializeApp() {
    // 检查登录状态
    if (!checkAuth()) {
        window.location.href = '/admin/login.html';
        return;
    }

    // 初始化事件监听器
    initializeEventListeners();
    
    // 更新时间显示
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // 加载仪表盘数据
    loadDashboard();
}

// 检查认证状态
function checkAuth() {
    const token = localStorage.getItem('adminToken');
    return token === 'cnmcnm66';
}

// 初始化事件监听器
function initializeEventListeners() {
    // 侧边栏菜单点击
    document.querySelectorAll('.menu-item').forEach(item => {
        item.addEventListener('click', function() {
            const page = this.dataset.page;
            switchPage(page);
        });
    });

    // 表单提交
    document.getElementById('generate-form').addEventListener('submit', handleGenerateCode);
    document.getElementById('bind-form').addEventListener('submit', handleBindDevice);
    
    // 状态筛选
    document.getElementById('status-filter').addEventListener('change', function() {
        currentFilter = this.value;
        currentPage = 1;
        loadCodesList();
    });
}

// 切换页面
function switchPage(pageName) {
    // 更新菜单状态
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-page="${pageName}"]`).classList.add('active');

    // 更新页面内容
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    document.getElementById(`${pageName}-page`).classList.add('active');

    // 更新页面标题
    const titles = {
        dashboard: '仪表盘',
        codes: '激活码管理',
        generate: '生成激活码',
        bind: '设备绑定',
        statistics: '统计报表',
        settings: '系统设置'
    };
    document.getElementById('page-title').textContent = titles[pageName];

    // 加载页面数据
    switch(pageName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'codes':
            loadCodesList();
            break;
        case 'statistics':
            loadStatistics();
            break;
        case 'settings':
            loadSettings();
            break;
    }
}

// 更新当前时间
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('zh-CN');
    document.getElementById('current-time').textContent = timeString;
}

// 显示加载状态
function showLoading() {
    document.getElementById('loading').style.display = 'flex';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loading').style.display = 'none';
}

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        }
    };

    const config = { ...defaultOptions, ...options };
    
    try {
        const response = await fetch(url, config);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || '请求失败');
        }
        
        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        showNotification('错误: ' + error.message, 'error');
        throw error;
    }
}

// 加载仪表盘数据
async function loadDashboard() {
    try {
        showLoading();
        
        // 获取统计数据
        const stats = await apiRequest(`/admin/statistics?adminToken=${ADMIN_TOKEN}`);
        
        if (stats.success) {
            document.getElementById('total-codes').textContent = stats.data.total;
            document.getElementById('bound-codes').textContent = stats.data.bound;
            document.getElementById('activated-codes').textContent = stats.data.activated;
            document.getElementById('expired-codes').textContent = stats.data.expired;
        }
        
        // 获取最近活动（这里可以扩展）
        loadRecentActivity();
        
    } catch (error) {
        console.error('加载仪表盘失败:', error);
    } finally {
        hideLoading();
    }
}

// 加载最近活动
function loadRecentActivity() {
    const activityList = document.getElementById('recent-activity');
    activityList.innerHTML = `
        <div class="activity-item">
            <i class="fas fa-info-circle"></i>
            <span>系统正常运行中</span>
            <small>${new Date().toLocaleString('zh-CN')}</small>
        </div>
    `;
}

// 加载激活码列表
async function loadCodesList() {
    try {
        showLoading();
        
        const response = await apiRequest(
            `/admin/activation-list?adminToken=${ADMIN_TOKEN}&page=${currentPage}&limit=20&status=${currentFilter}`
        );
        
        if (response.success) {
            renderCodesTable(response.data.list);
            renderPagination(response.data.total, response.data.page, response.data.limit);
        }
        
    } catch (error) {
        console.error('加载激活码列表失败:', error);
    } finally {
        hideLoading();
    }
}

// 渲染激活码表格
function renderCodesTable(codes) {
    const tbody = document.getElementById('codes-table-body');
    
    if (codes.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无数据</td></tr>';
        return;
    }
    
    tbody.innerHTML = codes.map(code => `
        <tr>
            <td><code>${code.code}</code></td>
            <td>${code.user_name}</td>
            <td>${code.device_id || '-'}</td>
            <td><span class="status-indicator status-${getStatusClass(code.status)}">${getStatusText(code.status)}</span></td>
            <td>${new Date(code.expire_date).toLocaleDateString('zh-CN')}</td>
            <td>${new Date(code.created_at).toLocaleString('zh-CN')}</td>
            <td>
                <button class="btn btn-sm" onclick="viewCodeDetails('${code.code}')" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                ${code.device_id ? `
                    <button class="btn btn-sm btn-warning" onclick="unbindDevice('${code.code}')" title="解绑设备">
                        <i class="fas fa-unlink"></i>
                    </button>
                ` : ''}
                <button class="btn btn-sm btn-danger" onclick="deleteCode('${code.code}')" title="删除激活码">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// 获取状态样式类
function getStatusClass(status) {
    const statusMap = {
        'activated': 'active',
        'bound': 'pending',
        'unbound': 'inactive',
        'expired': 'inactive'
    };
    return statusMap[status] || 'inactive';
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'activated': '已激活',
        'bound': '已绑定',
        'unbound': '未绑定',
        'expired': '已过期'
    };
    return statusMap[status] || '未知';
}

// 渲染分页
function renderPagination(total, page, limit) {
    const totalPages = Math.ceil(total / limit);
    const pagination = document.getElementById('codes-pagination');
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (page > 1) {
        paginationHTML += `<button onclick="changePage(${page - 1})">上一页</button>`;
    }
    
    // 页码
    for (let i = Math.max(1, page - 2); i <= Math.min(totalPages, page + 2); i++) {
        paginationHTML += `<button class="${i === page ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
    }
    
    // 下一页
    if (page < totalPages) {
        paginationHTML += `<button onclick="changePage(${page + 1})">下一页</button>`;
    }
    
    pagination.innerHTML = paginationHTML;
}

// 切换页码
function changePage(page) {
    currentPage = page;
    loadCodesList();
}

// 处理生成激活码
async function handleGenerateCode(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        userName: formData.get('userName'),
        expireDays: parseInt(formData.get('expireDays')),
        adminToken: ADMIN_TOKEN
    };
    
    try {
        showLoading();
        
        const response = await apiRequest('/admin/generate-code', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (response.success) {
            showGenerateResult(response.data);
            event.target.reset();
            showNotification('激活码生成成功！', 'success');
        }
        
    } catch (error) {
        console.error('生成激活码失败:', error);
    } finally {
        hideLoading();
    }
}

// 显示生成结果
function showGenerateResult(data) {
    const resultContainer = document.getElementById('generate-result');
    resultContainer.innerHTML = `
        <h4>生成成功</h4>
        <div class="result-item">
            <label>激活码:</label>
            <code style="font-size: 18px; font-weight: bold; color: #667eea;">${data.activationCode}</code>
            <button class="btn btn-sm" onclick="copyToClipboard('${data.activationCode}')">
                <i class="fas fa-copy"></i> 复制
            </button>
        </div>
        <div class="result-item">
            <label>用户名:</label>
            <span>${data.userName}</span>
        </div>
        <div class="result-item">
            <label>过期日期:</label>
            <span>${data.expireDate}</span>
        </div>
    `;
    resultContainer.style.display = 'block';
}

// 处理设备绑定
async function handleBindDevice(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const data = {
        activationCode: formData.get('activationCode'),
        deviceId: formData.get('deviceId'),
        adminToken: ADMIN_TOKEN
    };
    
    try {
        showLoading();
        
        const response = await apiRequest('/admin/bind-device', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (response.success) {
            showBindResult(response.data);
            event.target.reset();
            showNotification('设备绑定成功！', 'success');
        }
        
    } catch (error) {
        console.error('设备绑定失败:', error);
    } finally {
        hideLoading();
    }
}

// 显示绑定结果
function showBindResult(data) {
    const resultContainer = document.getElementById('bind-result');
    resultContainer.innerHTML = `
        <h4>绑定成功</h4>
        <div class="result-item">
            <label>激活码:</label>
            <code>${data.activationCode}</code>
        </div>
        <div class="result-item">
            <label>设备ID:</label>
            <code>${data.deviceId}</code>
        </div>
        <div class="result-item">
            <label>用户名:</label>
            <span>${data.userName}</span>
        </div>
    `;
    resultContainer.style.display = 'block';
}

// 生成设备ID
function generateDeviceId() {
    const chars = 'ABCDEF0123456789';
    let deviceId = 'AND-';
    for (let i = 0; i < 8; i++) {
        deviceId += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('device-id').value = deviceId;
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('已复制到剪贴板', 'success');
    }).catch(() => {
        showNotification('复制失败', 'error');
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: 600;
        z-index: 3000;
        animation: slideIn 0.3s ease;
    `;
    
    // 设置背景色
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// 刷新激活码列表
function refreshCodesList() {
    currentPage = 1;
    loadCodesList();
}

// 查看激活码详情
async function viewCodeDetails(code) {
    try {
        const response = await apiRequest(`/admin/activation-status/${code}?adminToken=${ADMIN_TOKEN}`);
        
        if (response.success) {
            showModal(`
                <h3>激活码详情</h3>
                <div class="detail-item">
                    <label>激活码:</label>
                    <code>${response.data.code}</code>
                </div>
                <div class="detail-item">
                    <label>用户名:</label>
                    <span>${response.data.user_name}</span>
                </div>
                <div class="detail-item">
                    <label>设备ID:</label>
                    <span>${response.data.device_id || '未绑定'}</span>
                </div>
                <div class="detail-item">
                    <label>状态:</label>
                    <span class="status-indicator status-${getStatusClass(response.data.status)}">${getStatusText(response.data.status)}</span>
                </div>
                <div class="detail-item">
                    <label>过期日期:</label>
                    <span>${new Date(response.data.expire_date).toLocaleDateString('zh-CN')}</span>
                </div>
                <div class="detail-item">
                    <label>创建时间:</label>
                    <span>${new Date(response.data.created_at).toLocaleString('zh-CN')}</span>
                </div>
                ${response.data.activated_at ? `
                    <div class="detail-item">
                        <label>激活时间:</label>
                        <span>${new Date(response.data.activated_at).toLocaleString('zh-CN')}</span>
                    </div>
                ` : ''}
            `);
        }
    } catch (error) {
        console.error('获取激活码详情失败:', error);
    }
}

// 解绑设备
async function unbindDevice(code) {
    if (!confirm('确定要解绑此设备吗？')) {
        return;
    }

    try {
        showLoading();

        const response = await apiRequest('/admin/unbind-device', {
            method: 'POST',
            body: JSON.stringify({
                activationCode: code,
                adminToken: ADMIN_TOKEN
            })
        });

        if (response.success) {
            showNotification('设备解绑成功！', 'success');
            loadCodesList();
        }

    } catch (error) {
        console.error('设备解绑失败:', error);
    } finally {
        hideLoading();
    }
}

// 删除激活码
async function deleteCode(code) {
    if (!confirm(`确定要删除激活码 ${code} 吗？\n\n此操作不可撤销！`)) {
        return;
    }

    try {
        showLoading();

        const response = await apiRequest('/admin/delete-code', {
            method: 'DELETE',
            body: JSON.stringify({
                activationCode: code,
                adminToken: ADMIN_TOKEN
            })
        });

        if (response.success) {
            showNotification('激活码删除成功！', 'success');
            loadCodesList();
            loadDashboard(); // 刷新仪表盘统计
        }

    } catch (error) {
        console.error('删除激活码失败:', error);
    } finally {
        hideLoading();
    }
}

// 显示模态框
function showModal(content) {
    document.getElementById('modal-body').innerHTML = content;
    document.getElementById('modal').style.display = 'block';
}

// 关闭模态框
function closeModal() {
    document.getElementById('modal').style.display = 'none';
}

// 加载统计数据
async function loadStatistics() {
    try {
        showLoading();
        
        const response = await apiRequest(`/admin/statistics?adminToken=${ADMIN_TOKEN}`);
        
        if (response.success) {
            renderStatisticsTable(response.data);
        }
        
    } catch (error) {
        console.error('加载统计数据失败:', error);
    } finally {
        hideLoading();
    }
}

// 渲染统计表格
function renderStatisticsTable(data) {
    const tbody = document.getElementById('stats-table-body');
    const total = data.total;
    
    tbody.innerHTML = `
        <tr>
            <td>总激活码数</td>
            <td>${data.total}</td>
            <td>100%</td>
        </tr>
        <tr>
            <td>已绑定设备</td>
            <td>${data.bound}</td>
            <td>${total > 0 ? ((data.bound / total) * 100).toFixed(1) : 0}%</td>
        </tr>
        <tr>
            <td>已激活</td>
            <td>${data.activated}</td>
            <td>${total > 0 ? ((data.activated / total) * 100).toFixed(1) : 0}%</td>
        </tr>
        <tr>
            <td>已过期</td>
            <td>${data.expired}</td>
            <td>${total > 0 ? ((data.expired / total) * 100).toFixed(1) : 0}%</td>
        </tr>
        <tr>
            <td>未绑定</td>
            <td>${data.unbound}</td>
            <td>${total > 0 ? ((data.unbound / total) * 100).toFixed(1) : 0}%</td>
        </tr>
    `;
}

// 加载系统设置
async function loadSettings() {
    try {
        const response = await apiRequest('/health');
        
        document.getElementById('server-status').textContent = '运行中';
        document.getElementById('server-status').className = 'status-indicator status-active';
        
        document.getElementById('db-status').textContent = response.database === 'connected' ? '已连接' : '未连接';
        document.getElementById('db-status').className = `status-indicator ${response.database === 'connected' ? 'status-active' : 'status-inactive'}`;
        
    } catch (error) {
        document.getElementById('server-status').textContent = '异常';
        document.getElementById('server-status').className = 'status-indicator status-inactive';
    }
}

// 清理过期激活码
async function cleanupExpiredCodes() {
    if (!confirm('确定要清理所有过期的激活码吗？此操作不可撤销。')) {
        return;
    }
    
    try {
        showLoading();
        // 这里可以添加清理API调用
        showNotification('清理操作完成', 'success');
    } catch (error) {
        console.error('清理失败:', error);
    } finally {
        hideLoading();
    }
}

// 导出数据
function exportData() {
    showNotification('导出功能开发中...', 'info');
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('adminToken');
        window.location.href = '/admin/login.html';
    }
}
