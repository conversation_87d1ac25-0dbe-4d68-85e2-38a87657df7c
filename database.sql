-- =====================================================
-- 激活码验证系统 - 数据库初始化脚本
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS activation_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE activation_system;

-- =====================================================
-- 删除已存在的表 (如果需要重新创建)
-- =====================================================
-- DROP TABLE IF EXISTS activation_codes;
-- DROP TABLE IF EXISTS admins;

-- =====================================================
-- 创建激活码表
-- =====================================================
CREATE TABLE IF NOT EXISTS activation_codes (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    code VARCHAR(19) UNIQUE NOT NULL COMMENT '激活码 (格式: XXXX-XXXX-XXXX-XXXX)',
    device_id VARCHAR(50) NULL COMMENT '绑定的设备ID (格式: AND-XXXXXXXX)',
    user_name VARCHAR(100) NOT NULL COMMENT '用户名',
    expire_date DATE NOT NULL COMMENT '过期日期',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    bound_at TIMESTAMP NULL COMMENT '绑定时间',
    bound_by VARCHAR(50) NULL COMMENT '绑定管理员',
    activated_at TIMESTAMP NULL COMMENT '激活时间',
    last_verify_at TIMESTAMP NULL COMMENT '最后验证时间',

    -- 索引
    INDEX idx_code (code),
    INDEX idx_device_id (device_id),
    INDEX idx_expire_date (expire_date),
    INDEX idx_created_at (created_at),
    INDEX idx_user_name (user_name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='激活码表';

-- =====================================================
-- 创建管理员表 (可选)
-- =====================================================
CREATE TABLE IF NOT EXISTS admins (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '管理员用户名',
    token VARCHAR(100) UNIQUE NOT NULL COMMENT '管理员令牌',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',

    -- 索引
    INDEX idx_username (username),
    INDEX idx_token (token),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- =====================================================
-- 创建操作日志表 (可选)
-- =====================================================
CREATE TABLE IF NOT EXISTS operation_logs (
    id INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    activation_code VARCHAR(19) NULL COMMENT '相关激活码',
    device_id VARCHAR(50) NULL COMMENT '相关设备ID',
    admin_user VARCHAR(50) NULL COMMENT '操作管理员',
    operation_data JSON NULL COMMENT '操作数据',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',

    -- 索引
    INDEX idx_operation_type (operation_type),
    INDEX idx_activation_code (activation_code),
    INDEX idx_device_id (device_id),
    INDEX idx_admin_user (admin_user),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- =====================================================
-- 插入默认管理员数据
-- =====================================================
INSERT INTO admins (username, token, is_active) VALUES
('admin', 'admin_secret_token', TRUE),
('system', 'system_admin_token', TRUE)
ON DUPLICATE KEY UPDATE 
    is_active = VALUES(is_active),
    last_login_at = NULL;

-- =====================================================
-- 插入测试激活码数据
-- =====================================================
INSERT INTO activation_codes (code, user_name, expire_date, is_active) VALUES
('TEST-1234-5678-9ABC', '测试用户1', DATE_ADD(CURDATE(), INTERVAL 30 DAY), TRUE),
('DEMO-ABCD-EFGH-IJKL', '测试用户2', DATE_ADD(CURDATE(), INTERVAL 60 DAY), TRUE),
('EVAL-MNOP-QRST-UVWX', '评估用户', DATE_ADD(CURDATE(), INTERVAL 7 DAY), TRUE)
ON DUPLICATE KEY UPDATE 
    user_name = VALUES(user_name),
    expire_date = VALUES(expire_date),
    is_active = VALUES(is_active);

-- =====================================================
-- 创建视图 - 激活码状态视图
-- =====================================================
CREATE OR REPLACE VIEW activation_status_view AS
SELECT 
    ac.id,
    ac.code,
    ac.device_id,
    ac.user_name,
    ac.expire_date,
    ac.is_active,
    ac.created_at,
    ac.bound_at,
    ac.bound_by,
    ac.activated_at,
    ac.last_verify_at,
    CASE 
        WHEN ac.expire_date < CURDATE() THEN 'expired'
        WHEN ac.device_id IS NULL THEN 'unbound'
        WHEN ac.activated_at IS NOT NULL THEN 'activated'
        WHEN ac.device_id IS NOT NULL THEN 'bound'
        ELSE 'created'
    END AS status,
    DATEDIFF(ac.expire_date, CURDATE()) AS remaining_days
FROM activation_codes ac;

-- =====================================================
-- 创建存储过程 - 清理过期激活码
-- =====================================================
DELIMITER //

CREATE PROCEDURE CleanupExpiredCodes()
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- 删除过期且未激活的激活码
    DELETE FROM activation_codes 
    WHERE expire_date < CURDATE() 
    AND activated_at IS NULL;
    
    SET deleted_count = ROW_COUNT();
    
    -- 记录清理操作
    INSERT INTO operation_logs (
        operation_type, 
        operation_data, 
        admin_user, 
        created_at
    ) VALUES (
        'cleanup_expired', 
        JSON_OBJECT('deleted_count', deleted_count),
        'system',
        NOW()
    );
    
    SELECT deleted_count AS cleaned_codes;
END //

DELIMITER ;

-- =====================================================
-- 创建存储过程 - 获取统计信息
-- =====================================================
DELIMITER //

CREATE PROCEDURE GetActivationStatistics()
BEGIN
    SELECT 
        COUNT(*) as total_codes,
        COUNT(device_id) as bound_codes,
        COUNT(activated_at) as activated_codes,
        COUNT(CASE WHEN expire_date < CURDATE() THEN 1 END) as expired_codes,
        COUNT(CASE WHEN device_id IS NULL THEN 1 END) as unbound_codes,
        COUNT(CASE WHEN last_verify_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_last_week
    FROM activation_codes;
END //

DELIMITER ;

-- =====================================================
-- 创建触发器 - 记录激活码操作日志
-- =====================================================
DELIMITER //

CREATE TRIGGER activation_codes_after_insert
AFTER INSERT ON activation_codes
FOR EACH ROW
BEGIN
    INSERT INTO operation_logs (
        operation_type,
        activation_code,
        operation_data,
        admin_user,
        created_at
    ) VALUES (
        'create_code',
        NEW.code,
        JSON_OBJECT(
            'user_name', NEW.user_name,
            'expire_date', NEW.expire_date
        ),
        'system',
        NOW()
    );
END //

CREATE TRIGGER activation_codes_after_update
AFTER UPDATE ON activation_codes
FOR EACH ROW
BEGIN
    DECLARE operation_type VARCHAR(50) DEFAULT 'update_code';
    
    -- 判断操作类型
    IF OLD.device_id IS NULL AND NEW.device_id IS NOT NULL THEN
        SET operation_type = 'bind_device';
    ELSEIF OLD.device_id IS NOT NULL AND NEW.device_id IS NULL THEN
        SET operation_type = 'unbind_device';
    ELSEIF OLD.activated_at IS NULL AND NEW.activated_at IS NOT NULL THEN
        SET operation_type = 'activate_code';
    ELSEIF OLD.expire_date != NEW.expire_date THEN
        SET operation_type = 'extend_expire';
    END IF;
    
    INSERT INTO operation_logs (
        operation_type,
        activation_code,
        device_id,
        operation_data,
        admin_user,
        created_at
    ) VALUES (
        operation_type,
        NEW.code,
        NEW.device_id,
        JSON_OBJECT(
            'old_device_id', OLD.device_id,
            'new_device_id', NEW.device_id,
            'old_expire_date', OLD.expire_date,
            'new_expire_date', NEW.expire_date
        ),
        'system',
        NOW()
    );
END //

DELIMITER ;

-- =====================================================
-- 显示创建结果
-- =====================================================
SHOW TABLES;

SELECT 'Database initialization completed successfully!' AS status;

-- 显示测试数据
SELECT 'Test activation codes:' AS info;
SELECT code, user_name, expire_date, status, remaining_days 
FROM activation_status_view 
ORDER BY created_at DESC;

-- 显示统计信息
CALL GetActivationStatistics();
