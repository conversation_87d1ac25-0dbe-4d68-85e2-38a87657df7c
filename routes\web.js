const express = require('express');
const path = require('path');
const router = express.Router();

/**
 * Web管理后台路由
 * 处理静态文件服务和页面路由
 */

// 管理后台登录页
router.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/admin/login.html'));
});

// 管理后台首页 - 需要登录验证
router.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/admin/index.html'));
});

// 处理其他路径
router.get('/*', (req, res) => {
    // 默认重定向到登录页
    res.redirect('/admin/login');
});

module.exports = router;
