const express = require('express');
const router = express.Router();

const { DatabaseOperations } = require('../config/database');
const { verifyAdminToken } = require('../middleware/auth');
const { 
    generateUniqueActivationCode,
    validateActivationCode, 
    validateDeviceId,
    validateUserName,
    validateExpireDays,
    calculateExpireDate,
    getActivationStatus
} = require('../utils/activation');

/**
 * 管理员生成激活码接口
 * POST /admin/generate-code
 * 
 * 请求格式:
 * {
 *   "userName": "用户名",
 *   "expireDays": 30,
 *   "adminToken": "admin_secret_token"
 * }
 */
router.post('/generate-code', verifyAdminToken, async (req, res) => {
    try {
        const { userName, expireDays = 30 } = req.body;

        // 验证用户名
        if (!validateUserName(userName)) {
            return res.status(400).json({
                success: false,
                message: "用户名不能为空且长度不能超过100个字符"
            });
        }

        // 验证有效期天数
        if (!validateExpireDays(expireDays)) {
            return res.status(400).json({
                success: false,
                message: "有效期天数必须是1-3650之间的整数"
            });
        }

        // 生成唯一激活码
        const newCode = await generateUniqueActivationCode(
            async (code) => await DatabaseOperations.checkCodeExists(code)
        );

        // 计算过期日期
        const expireDate = calculateExpireDate(expireDays);

        // 保存到数据库
        await DatabaseOperations.createActivationCode({
            code: newCode,
            userName: userName,
            expireDate: expireDate
        });

        res.json({
            success: true,
            message: "激活码生成成功",
            data: {
                activationCode: newCode,
                userName: userName,
                expireDate: expireDate,
                expireDays: expireDays
            }
        });

        console.log(`Generated activation code: ${newCode} for user: ${userName}`);

    } catch (error) {
        console.error('Generate code error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，激活码生成失败"
        });
    }
});

/**
 * 管理员绑定设备接口
 * POST /admin/bind-device
 * 
 * 请求格式:
 * {
 *   "activationCode": "ABCD-EFGH-IJKL-MNOP",
 *   "deviceId": "AND-12345678",
 *   "adminToken": "admin_secret_token"
 * }
 */
router.post('/bind-device', verifyAdminToken, async (req, res) => {
    try {
        const { activationCode, deviceId } = req.body;

        // 验证激活码格式
        if (!validateActivationCode(activationCode)) {
            return res.status(400).json({
                success: false,
                message: "激活码格式错误"
            });
        }

        // 验证设备ID格式
        if (!validateDeviceId(deviceId)) {
            return res.status(400).json({
                success: false,
                message: "设备ID格式错误"
            });
        }

        // 查询激活码
        const codeData = await DatabaseOperations.getActivationCode(activationCode);
        if (!codeData) {
            return res.status(404).json({
                success: false,
                message: "激活码不存在"
            });
        }

        // 检查是否已绑定其他设备
        if (codeData.device_id && codeData.device_id !== deviceId) {
            return res.status(409).json({
                success: false,
                message: `激活码已绑定设备: ${codeData.device_id}`
            });
        }

        // 绑定设备
        await DatabaseOperations.updateActivationCode(activationCode, {
            device_id: deviceId,
            bound_at: new Date(),
            bound_by: 'admin'
        });

        res.json({
            success: true,
            message: "设备绑定成功",
            data: {
                activationCode: activationCode,
                deviceId: deviceId,
                userName: codeData.user_name,
                expireDate: codeData.expire_date
            }
        });

        console.log(`Bound device: ${deviceId} to activation code: ${activationCode}`);

    } catch (error) {
        console.error('Bind device error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，设备绑定失败"
        });
    }
});

/**
 * 管理员查询激活码状态接口
 * GET /admin/activation-status/:code?adminToken=xxx
 */
router.get('/activation-status/:code', verifyAdminToken, async (req, res) => {
    try {
        const { code } = req.params;

        // 验证激活码格式
        if (!validateActivationCode(code)) {
            return res.status(400).json({
                success: false,
                message: "激活码格式错误"
            });
        }

        // 查询激活码
        const codeData = await DatabaseOperations.getActivationCode(code);
        if (!codeData) {
            return res.status(404).json({
                success: false,
                message: "激活码不存在"
            });
        }

        // 添加状态信息
        const status = getActivationStatus(codeData);
        const responseData = {
            ...codeData,
            status: status
        };

        res.json({
            success: true,
            data: responseData
        });

    } catch (error) {
        console.error('Query status error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，查询失败"
        });
    }
});

/**
 * 管理员激活码列表接口
 * GET /admin/activation-list?adminToken=xxx&page=1&limit=20&status=all
 */
router.get('/activation-list', verifyAdminToken, async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 20, 
            status = 'all' 
        } = req.query;

        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);

        // 验证分页参数
        if (pageNum < 1 || limitNum < 1 || limitNum > 100) {
            return res.status(400).json({
                success: false,
                message: "分页参数错误"
            });
        }

        // 获取激活码列表
        const result = await DatabaseOperations.getActivationList(pageNum, limitNum, status);

        // 为每个激活码添加状态信息
        const listWithStatus = result.list.map(item => ({
            ...item,
            status: getActivationStatus(item)
        }));

        res.json({
            success: true,
            data: {
                total: result.total,
                page: pageNum,
                limit: limitNum,
                list: listWithStatus
            }
        });

    } catch (error) {
        console.error('List activation codes error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，获取列表失败"
        });
    }
});

/**
 * 管理员解绑设备接口
 * POST /admin/unbind-device
 *
 * 请求格式:
 * {
 *   "activationCode": "ABCD-EFGH-IJKL-MNOP",
 *   "adminToken": "admin_secret_token"
 * }
 */
router.post('/unbind-device', verifyAdminToken, async (req, res) => {
    try {
        const { activationCode } = req.body;

        // 验证激活码格式
        if (!validateActivationCode(activationCode)) {
            return res.status(400).json({
                success: false,
                message: "激活码格式错误"
            });
        }

        // 查询激活码
        const codeData = await DatabaseOperations.getActivationCode(activationCode);
        if (!codeData) {
            return res.status(404).json({
                success: false,
                message: "激活码不存在"
            });
        }

        // 检查是否已绑定设备
        if (!codeData.device_id) {
            return res.status(400).json({
                success: false,
                message: "激活码未绑定任何设备"
            });
        }

        // 解绑设备
        await DatabaseOperations.updateActivationCode(activationCode, {
            device_id: null,
            bound_at: null,
            bound_by: null,
            activated_at: null,
            last_verify_at: null
        });

        res.json({
            success: true,
            message: "设备解绑成功",
            data: {
                activationCode: activationCode,
                previousDeviceId: codeData.device_id
            }
        });

        console.log(`Unbound device: ${codeData.device_id} from activation code: ${activationCode}`);

    } catch (error) {
        console.error('Unbind device error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，设备解绑失败"
        });
    }
});

/**
 * 管理员延长有效期接口
 * POST /admin/extend-expire
 *
 * 请求格式:
 * {
 *   "activationCode": "ABCD-EFGH-IJKL-MNOP",
 *   "expireDays": 30,
 *   "adminToken": "admin_secret_token"
 * }
 */
router.post('/extend-expire', verifyAdminToken, async (req, res) => {
    try {
        const { activationCode, expireDays } = req.body;

        // 验证激活码格式
        if (!validateActivationCode(activationCode)) {
            return res.status(400).json({
                success: false,
                message: "激活码格式错误"
            });
        }

        // 验证有效期天数
        if (!validateExpireDays(expireDays)) {
            return res.status(400).json({
                success: false,
                message: "有效期天数必须是1-3650之间的整数"
            });
        }

        // 查询激活码
        const codeData = await DatabaseOperations.getActivationCode(activationCode);
        if (!codeData) {
            return res.status(404).json({
                success: false,
                message: "激活码不存在"
            });
        }

        // 计算新的过期日期 (从当前日期开始计算)
        const newExpireDate = calculateExpireDate(expireDays);

        // 更新过期日期
        await DatabaseOperations.updateActivationCode(activationCode, {
            expire_date: newExpireDate
        });

        res.json({
            success: true,
            message: "有效期延长成功",
            data: {
                activationCode: activationCode,
                oldExpireDate: codeData.expire_date,
                newExpireDate: newExpireDate,
                extendedDays: expireDays
            }
        });

        console.log(`Extended expire date for: ${activationCode} to ${newExpireDate}`);

    } catch (error) {
        console.error('Extend expire error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，延长有效期失败"
        });
    }
});

/**
 * 管理员获取统计信息接口
 * GET /admin/statistics?adminToken=xxx
 */
router.get('/statistics', verifyAdminToken, async (req, res) => {
    try {
        const stats = await DatabaseOperations.getStatistics();

        res.json({
            success: true,
            data: {
                total: stats.total,
                bound: stats.bound,
                activated: stats.activated,
                expired: stats.expired,
                unbound: stats.total - stats.bound
            }
        });

    } catch (error) {
        console.error('Get statistics error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，获取统计信息失败"
        });
    }
});

/**
 * 管理员删除激活码接口
 * DELETE /admin/delete-code
 *
 * 请求格式:
 * {
 *   "activationCode": "ABCD-EFGH-IJKL-MNOP",
 *   "adminToken": "cnmcnm66"
 * }
 */
router.delete('/delete-code', verifyAdminToken, async (req, res) => {
    try {
        const { activationCode } = req.body;

        // 验证激活码格式
        if (!validateActivationCode(activationCode)) {
            return res.status(400).json({
                success: false,
                message: "激活码格式错误"
            });
        }

        // 检查激活码是否存在
        const codeData = await getActivationStatus(activationCode);
        if (!codeData) {
            return res.status(404).json({
                success: false,
                message: "激活码不存在"
            });
        }

        // 删除激活码
        const deleted = await DatabaseOperations.deleteActivationCode(activationCode);

        if (!deleted) {
            return res.status(500).json({
                success: false,
                message: "删除失败"
            });
        }

        res.json({
            success: true,
            message: "激活码删除成功",
            data: {
                deletedCode: activationCode,
                userName: codeData.user_name
            }
        });

        console.log(`Deleted activation code: ${activationCode} (user: ${codeData.user_name})`);

    } catch (error) {
        console.error('Delete activation code error:', error);
        res.status(500).json({
            success: false,
            message: "服务器错误，删除激活码失败"
        });
    }
});

module.exports = router;
