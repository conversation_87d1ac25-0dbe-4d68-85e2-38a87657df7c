<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活码管理系统</title>
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-key"></i> 激活码管理</h2>
            </div>
            <ul class="sidebar-menu">
                <li class="menu-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>仪表盘</span>
                </li>
                <li class="menu-item" data-page="codes">
                    <i class="fas fa-list"></i>
                    <span>激活码管理</span>
                </li>
                <li class="menu-item" data-page="generate">
                    <i class="fas fa-plus-circle"></i>
                    <span>生成激活码</span>
                </li>
                <li class="menu-item" data-page="bind">
                    <i class="fas fa-link"></i>
                    <span>设备绑定</span>
                </li>
                <li class="menu-item" data-page="statistics">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计报表</span>
                </li>
                <li class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </li>
            </ul>
            <div class="sidebar-footer">
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出登录</span>
                </button>
            </div>
        </nav>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="header">
                <div class="header-left">
                    <h1 id="page-title">仪表盘</h1>
                </div>
                <div class="header-right">
                    <span class="user-info">
                        <i class="fas fa-user"></i>
                        管理员
                    </span>
                    <span class="current-time" id="current-time"></span>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="content">
                <!-- 仪表盘页面 -->
                <div id="dashboard-page" class="page active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-key"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="total-codes">0</h3>
                                <p>总激活码数</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-link"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="bound-codes">0</h3>
                                <p>已绑定设备</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activated-codes">0</h3>
                                <p>已激活</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="expired-codes">0</h3>
                                <p>已过期</p>
                            </div>
                        </div>
                    </div>

                    <div class="recent-activity">
                        <h3>最近活动</h3>
                        <div class="activity-list" id="recent-activity">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                </div>

                <!-- 激活码管理页面 -->
                <div id="codes-page" class="page">
                    <div class="page-header">
                        <h3>激活码列表</h3>
                        <div class="page-actions">
                            <select id="status-filter">
                                <option value="all">全部状态</option>
                                <option value="unbound">未绑定</option>
                                <option value="bound">已绑定</option>
                                <option value="activated">已激活</option>
                                <option value="expired">已过期</option>
                            </select>
                            <button class="btn btn-primary" onclick="refreshCodesList()">
                                <i class="fas fa-refresh"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>激活码</th>
                                    <th>用户名</th>
                                    <th>设备ID</th>
                                    <th>状态</th>
                                    <th>过期日期</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="codes-table-body">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    <div class="pagination" id="codes-pagination">
                        <!-- 动态加载 -->
                    </div>
                </div>

                <!-- 生成激活码页面 -->
                <div id="generate-page" class="page">
                    <div class="form-container">
                        <h3>生成新激活码</h3>
                        <form id="generate-form" class="form">
                            <div class="form-group">
                                <label for="user-name">用户名</label>
                                <input type="text" id="user-name" name="userName" required>
                            </div>
                            <div class="form-group">
                                <label for="expire-days">有效期（天）</label>
                                <input type="number" id="expire-days" name="expireDays" value="30" min="1" max="3650" required>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 生成激活码
                                </button>
                                <button type="reset" class="btn btn-secondary">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            </div>
                        </form>
                        <div id="generate-result" class="result-container" style="display: none;">
                            <!-- 生成结果 -->
                        </div>
                    </div>
                </div>

                <!-- 设备绑定页面 -->
                <div id="bind-page" class="page">
                    <div class="form-container">
                        <h3>绑定设备</h3>
                        <form id="bind-form" class="form">
                            <div class="form-group">
                                <label for="activation-code">激活码</label>
                                <input type="text" id="activation-code" name="activationCode" placeholder="XXXX-XXXX-XXXX-XXXX" required>
                            </div>
                            <div class="form-group">
                                <label for="device-id">设备ID</label>
                                <input type="text" id="device-id" name="deviceId" placeholder="AND-XXXXXXXX" required>
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-link"></i> 绑定设备
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="generateDeviceId()">
                                    <i class="fas fa-random"></i> 生成设备ID
                                </button>
                            </div>
                        </form>
                        <div id="bind-result" class="result-container" style="display: none;">
                            <!-- 绑定结果 -->
                        </div>
                    </div>
                </div>

                <!-- 统计报表页面 -->
                <div id="statistics-page" class="page">
                    <h3>统计报表</h3>
                    <div class="stats-detail">
                        <div class="chart-container">
                            <canvas id="status-chart"></canvas>
                        </div>
                        <div class="stats-table">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>统计项</th>
                                        <th>数量</th>
                                        <th>百分比</th>
                                    </tr>
                                </thead>
                                <tbody id="stats-table-body">
                                    <!-- 动态加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 系统设置页面 -->
                <div id="settings-page" class="page">
                    <h3>系统设置</h3>
                    <div class="settings-container">
                        <div class="setting-group">
                            <h4>系统信息</h4>
                            <div class="setting-item">
                                <label>服务器状态</label>
                                <span id="server-status" class="status-indicator">运行中</span>
                            </div>
                            <div class="setting-item">
                                <label>数据库状态</label>
                                <span id="db-status" class="status-indicator">已连接</span>
                            </div>
                            <div class="setting-item">
                                <label>系统版本</label>
                                <span>1.0.0</span>
                            </div>
                        </div>
                        <div class="setting-group">
                            <h4>维护操作</h4>
                            <button class="btn btn-warning" onclick="cleanupExpiredCodes()">
                                <i class="fas fa-trash"></i> 清理过期激活码
                            </button>
                            <button class="btn btn-info" onclick="exportData()">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modal-body">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 加载指示器 -->
    <div id="loading" class="loading">
        <div class="spinner"></div>
        <p>加载中...</p>
    </div>

    <script src="js/admin.js"></script>
</body>
</html>
