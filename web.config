<?xml version="1.0" encoding="utf-8"?>
<!--
  激活码验证系统 - IIS 配置文件
  用于配置 IIS 反向代理到 Node.js 应用
-->
<configuration>
  <system.webServer>
    
    <!-- URL 重写规则 -->
    <rewrite>
      <rules>
        <!-- API 接口代理 -->
        <rule name="API Proxy" stopProcessing="true">
          <match url="^api/(.*)" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="http://localhost:3001/api/{R:1}" />
        </rule>
        
        <!-- 管理员接口代理 -->
        <rule name="Admin API Proxy" stopProcessing="true">
          <match url="^admin/(generate-code|bind-device|activation-status|activation-list|unbind-device|extend-expire|statistics)" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
          </conditions>
          <action type="Rewrite" url="http://localhost:3001/admin/{R:0}" />
        </rule>
        
        <!-- 健康检查代理 -->
        <rule name="Health Check Proxy" stopProcessing="true">
          <match url="^health$" />
          <action type="Rewrite" url="http://localhost:3001/health" />
        </rule>
        
        <!-- 管理后台静态文件代理 -->
        <rule name="Admin Static Proxy" stopProcessing="true">
          <match url="^admin/(.*)" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="http://localhost:3001/admin/{R:1}" />
        </rule>
        
        <!-- 默认代理规则 -->
        <rule name="Default Proxy" stopProcessing="true">
          <match url="(.*)" />
          <conditions>
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="http://localhost:3001/{R:1}" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- 默认文档 -->
    <defaultDocument>
      <files>
        <clear />
        <add value="index.html" />
      </files>
    </defaultDocument>
    
    <!-- 静态内容 -->
    <staticContent>
      <!-- JSON 文件 -->
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <!-- 字体文件 -->
      <mimeMap fileExtension=".woff" mimeType="application/font-woff" />
      <mimeMap fileExtension=".woff2" mimeType="application/font-woff2" />
      <!-- 移除服务器头 -->
      <remove fileExtension=".js" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
    </staticContent>
    
    <!-- HTTP 响应头 -->
    <httpProtocol>
      <customHeaders>
        <!-- 安全头 -->
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
        <add name="Content-Security-Policy" value="default-src 'self'; script-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; font-src 'self' cdnjs.cloudflare.com; img-src 'self' data:;" />
        
        <!-- CORS 头 (如果需要) -->
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST, PUT, DELETE, OPTIONS" />
        <add name="Access-Control-Allow-Headers" value="Content-Type, Authorization, Admin-Token, X-Encryption" />
        
        <!-- 缓存控制 -->
        <add name="Cache-Control" value="no-cache, no-store, must-revalidate" />
        <add name="Pragma" value="no-cache" />
        <add name="Expires" value="0" />
      </customHeaders>
    </httpProtocol>
    
    <!-- 压缩 -->
    <httpCompression>
      <dynamicTypes>
        <add mimeType="application/json" enabled="true" />
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="text/css" enabled="true" />
        <add mimeType="text/html" enabled="true" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="application/javascript" enabled="true" />
        <add mimeType="text/css" enabled="true" />
        <add mimeType="text/html" enabled="true" />
      </staticTypes>
    </httpCompression>
    
    <!-- 错误页面 -->
    <httpErrors errorMode="Custom" defaultResponseMode="ExecuteURL">
      <remove statusCode="404" subStatusCode="-1" />
      <error statusCode="404" path="/404.html" responseMode="ExecuteURL" />
      <remove statusCode="500" subStatusCode="-1" />
      <error statusCode="500" path="/500.html" responseMode="ExecuteURL" />
    </httpErrors>
    
    <!-- 请求过滤 -->
    <security>
      <requestFiltering>
        <!-- 限制请求大小 -->
        <requestLimits maxAllowedContentLength="10485760" /> <!-- 10MB -->
        
        <!-- 隐藏文件扩展名 -->
        <hiddenSegments>
          <add segment="node_modules" />
          <add segment=".env" />
          <add segment=".git" />
          <add segment="logs" />
        </hiddenSegments>
        
        <!-- 文件扩展名过滤 -->
        <fileExtensions>
          <add fileExtension=".env" allowed="false" />
          <add fileExtension=".log" allowed="false" />
        </fileExtensions>
      </requestFiltering>
    </security>
    
    <!-- 模块 -->
    <modules>
      <!-- 移除不需要的模块 -->
      <remove name="DefaultDocumentModule" />
      <remove name="DirectoryListingModule" />
      <add name="DefaultDocumentModule" />
    </modules>
    
    <!-- 处理程序 -->
    <handlers>
      <!-- 静态文件处理 -->
      <add name="StaticFile" path="*" verb="*" modules="StaticFileModule,DefaultDocumentModule,DirectoryListingModule" resourceType="Either" requireAccess="Read" />
    </handlers>
    
  </system.webServer>
  
  <!-- 应用程序设置 -->
  <appSettings>
    <add key="NODE_ENV" value="production" />
    <add key="PORT" value="3001" />
  </appSettings>
  
  <!-- 连接字符串 (如果需要) -->
  <connectionStrings>
    <add name="DefaultConnection" connectionString="Server=localhost;Database=activation_system;Uid=root;Pwd=password;" />
  </connectionStrings>
  
</configuration>
