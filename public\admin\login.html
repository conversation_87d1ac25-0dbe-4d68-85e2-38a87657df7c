<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 激活码管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-header {
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        .login-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            position: relative;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 600;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-group i {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            margin-top: 12px;
        }

        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .loading {
            display: none;
            margin-top: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 12px;
        }

        .demo-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: left;
        }

        .demo-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .demo-info p {
            color: #424242;
            font-size: 13px;
            margin-bottom: 5px;
        }

        .demo-info code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-key"></i> 管理员登录</h1>
            <p>激活码管理系统</p>
        </div>

        <div class="demo-info">
            <h4><i class="fas fa-info-circle"></i> 登录说明</h4>
            <p>请使用管理员令牌登录</p>
        </div>

        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <form id="login-form" class="login-form">
            <div class="form-group">
                <label for="admin-token">管理员令牌</label>
                <input type="password" id="admin-token" name="adminToken" placeholder="请输入管理员令牌" required>
                <i class="fas fa-key"></i>
            </div>

            <button type="submit" class="login-btn" id="login-btn">
                <span id="login-text">登录</span>
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>

        <div class="login-footer">
            <p>&copy; 2024 激活码管理系统. 保留所有权利.</p>
        </div>
    </div>

    <script>
        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('adminToken')) {
                window.location.href = '/admin/';
            }
        });

        // 登录表单提交
        document.getElementById('login-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const adminToken = document.getElementById('admin-token').value;

            if (!adminToken) {
                showError('请输入管理员令牌');
                return;
            }

            await handleTokenLogin(adminToken);
        });

        // 处理令牌登录
        async function handleTokenLogin(adminToken) {
            setLoading(true);
            hideMessages();

            try {
                // 验证管理员令牌
                if (adminToken === 'cnmcnm66') {
                    // 模拟API调用延迟
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // 设置管理员令牌
                    localStorage.setItem('adminToken', adminToken);

                    showSuccess('登录成功，正在跳转...');

                    setTimeout(() => {
                        window.location.href = '/admin/';
                    }, 1500);
                } else {
                    throw new Error('无效的管理员令牌');
                }
            } catch (error) {
                showError(error.message);
            } finally {
                setLoading(false);
            }
        }

        // 设置加载状态
        function setLoading(loading) {
            const btn = document.getElementById('login-btn');
            const text = document.getElementById('login-text');
            const spinner = document.getElementById('loading');
            
            if (loading) {
                btn.disabled = true;
                text.style.display = 'none';
                spinner.style.display = 'block';
            } else {
                btn.disabled = false;
                text.style.display = 'inline';
                spinner.style.display = 'none';
            }
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }

        // 隐藏消息
        function hideMessages() {
            document.getElementById('error-message').style.display = 'none';
            document.getElementById('success-message').style.display = 'none';
        }

        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('login-form').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>
