const crypto = require('crypto');
require('dotenv').config();

// 固定的加密配置 (根据文档要求)
const FIXED_ENCRYPTION_KEY = "XwzcActivation24"; // 16字节
const FIXED_ENCRYPTION_IV = "InitVector123456";  // 16字节

/**
 * AES-128-CBC 加密函数
 * @param {string} data - 要加密的数据
 * @returns {string} - Base64编码的加密数据
 */
function aesEncrypt(data) {
    try {
        if (!data) {
            throw new Error('Data to encrypt cannot be empty');
        }

        const cipher = crypto.createCipheriv('aes-128-cbc', FIXED_ENCRYPTION_KEY, FIXED_ENCRYPTION_IV);
        let encrypted = cipher.update(data, 'utf8', 'base64');
        encrypted += cipher.final('base64');
        
        return encrypted;
    } catch (error) {
        console.error('Encryption error:', error);
        throw new Error('Encryption failed');
    }
}

/**
 * AES-128-CBC 解密函数
 * @param {string} encryptedData - Base64编码的加密数据
 * @returns {string} - 解密后的原始数据
 */
function aesDecrypt(encryptedData) {
    try {
        if (!encryptedData) {
            throw new Error('Encrypted data cannot be empty');
        }

        const decipher = crypto.createDecipheriv('aes-128-cbc', FIXED_ENCRYPTION_KEY, FIXED_ENCRYPTION_IV);
        let decrypted = decipher.update(encryptedData, 'base64', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    } catch (error) {
        console.error('Decryption error:', error);
        throw new Error('Decryption failed');
    }
}

/**
 * 加密JSON对象
 * @param {Object} jsonData - 要加密的JSON对象
 * @returns {string} - Base64编码的加密数据
 */
function encryptJson(jsonData) {
    try {
        const jsonString = JSON.stringify(jsonData);
        return aesEncrypt(jsonString);
    } catch (error) {
        console.error('JSON encryption error:', error);
        throw new Error('JSON encryption failed');
    }
}

/**
 * 解密并解析JSON对象
 * @param {string} encryptedData - Base64编码的加密数据
 * @returns {Object} - 解密后的JSON对象
 */
function decryptJson(encryptedData) {
    try {
        const decryptedString = aesDecrypt(encryptedData);
        return JSON.parse(decryptedString);
    } catch (error) {
        console.error('JSON decryption error:', error);
        throw new Error('JSON decryption failed');
    }
}

/**
 * 验证加密配置
 * @returns {boolean} - 配置是否正确
 */
function validateEncryptionConfig() {
    try {
        // 测试加密解密是否正常工作
        const testData = 'test_encryption_config';
        const encrypted = aesEncrypt(testData);
        const decrypted = aesDecrypt(encrypted);
        
        return decrypted === testData;
    } catch (error) {
        console.error('Encryption config validation failed:', error);
        return false;
    }
}

/**
 * 生成随机字符串 (用于测试)
 * @param {number} length - 字符串长度
 * @returns {string} - 随机字符串
 */
function generateRandomString(length = 16) {
    return crypto.randomBytes(length).toString('hex').substring(0, length);
}

/**
 * 计算字符串的MD5哈希值
 * @param {string} data - 要计算哈希的数据
 * @returns {string} - MD5哈希值
 */
function md5Hash(data) {
    return crypto.createHash('md5').update(data).digest('hex');
}

/**
 * 计算字符串的SHA256哈希值
 * @param {string} data - 要计算哈希的数据
 * @returns {string} - SHA256哈希值
 */
function sha256Hash(data) {
    return crypto.createHash('sha256').update(data).digest('hex');
}

module.exports = {
    aesEncrypt,
    aesDecrypt,
    encryptJson,
    decryptJson,
    validateEncryptionConfig,
    generateRandomString,
    md5Hash,
    sha256Hash,
    FIXED_ENCRYPTION_KEY,
    FIXED_ENCRYPTION_IV
};
