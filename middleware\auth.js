const { aesDecrypt, encryptJson } = require('../utils/encryption');
require('dotenv').config();

const ADMIN_TOKEN = process.env.ADMIN_TOKEN || "cnmcnm66";

/**
 * 解析加密请求的中间件
 * 解密请求体中的encrypted字段，并将解密后的数据存储在req.decryptedData中
 */
function parseEncryptedRequest(req, res, next) {
    try {
        const { encrypted } = req.body;

        if (!encrypted) {
            return res.status(400).json({ 
                success: false,
                error: 'Missing encrypted data' 
            });
        }

        // 解密数据
        const decrypted = aesDecrypt(encrypted);
        req.decryptedData = JSON.parse(decrypted);
        
        console.log('Decrypted request data:', req.decryptedData);
        next();
    } catch (error) {
        console.error('Parse encrypted request error:', error);
        return res.status(400).json({ 
            success: false,
            error: 'Invalid request format or decryption failed' 
        });
    }
}

/**
 * 发送加密响应的辅助函数
 * @param {Object} res - Express响应对象
 * @param {Object} data - 要加密的响应数据
 * @param {number} statusCode - HTTP状态码 (默认200)
 */
function sendEncryptedResponse(res, data, statusCode = 200) {
    try {
        const encrypted = encryptJson(data);
        
        if (!encrypted) {
            return res.status(500).json({ 
                success: false,
                error: 'Encryption failed' 
            });
        }

        console.log('Sending encrypted response:', data);
        res.status(statusCode).json({ encrypted });
    } catch (error) {
        console.error('Send encrypted response error:', error);
        res.status(500).json({ 
            success: false,
            error: 'Response encryption failed' 
        });
    }
}

/**
 * 验证管理员权限的中间件
 * 检查请求中的adminToken是否正确
 */
function verifyAdminToken(req, res, next) {
    try {
        // 从请求体或查询参数中获取token
        const token = req.body.adminToken || req.query.adminToken || req.headers['admin-token'];

        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Admin token is required'
            });
        }

        if (token !== ADMIN_TOKEN) {
            console.log('Invalid admin token attempt:', token);
            return res.status(401).json({
                success: false,
                message: 'Unauthorized: Invalid admin token'
            });
        }

        console.log('Admin token verified successfully');
        next();
    } catch (error) {
        console.error('Admin token verification error:', error);
        res.status(500).json({
            success: false,
            message: 'Token verification failed'
        });
    }
}

/**
 * 请求日志中间件
 * 记录所有API请求的基本信息
 */
function requestLogger(req, res, next) {
    const timestamp = new Date().toISOString();
    const method = req.method;
    const url = req.originalUrl;
    const ip = req.ip || req.connection.remoteAddress;
    
    console.log(`[${timestamp}] ${method} ${url} - IP: ${ip}`);
    
    // 记录请求体大小 (不记录具体内容以保护隐私)
    if (req.body && Object.keys(req.body).length > 0) {
        console.log(`Request body size: ${JSON.stringify(req.body).length} bytes`);
    }
    
    next();
}

/**
 * 错误处理中间件
 * 统一处理应用程序中的错误
 */
function errorHandler(err, req, res, next) {
    console.error('Error occurred:', err);
    
    // 如果响应已经发送，则交给默认的Express错误处理器
    if (res.headersSent) {
        return next(err);
    }
    
    // 根据错误类型返回不同的响应
    if (err.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            error: err.message
        });
    }
    
    if (err.name === 'UnauthorizedError') {
        return res.status(401).json({
            success: false,
            message: 'Unauthorized access'
        });
    }
    
    if (err.code === 'ER_DUP_ENTRY') {
        return res.status(409).json({
            success: false,
            message: 'Duplicate entry'
        });
    }
    
    // 默认服务器错误
    res.status(500).json({
        success: false,
        message: 'Internal server error'
    });
}

/**
 * 验证请求体必需字段的中间件工厂
 * @param {Array} requiredFields - 必需字段数组
 * @returns {Function} - 中间件函数
 */
function validateRequiredFields(requiredFields) {
    return (req, res, next) => {
        const data = req.decryptedData || req.body;
        const missingFields = [];
        
        for (const field of requiredFields) {
            if (!data[field]) {
                missingFields.push(field);
            }
        }
        
        if (missingFields.length > 0) {
            return res.status(400).json({
                success: false,
                message: `Missing required fields: ${missingFields.join(', ')}`
            });
        }
        
        next();
    };
}

/**
 * CORS配置中间件
 */
function corsConfig(req, res, next) {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Admin-Token, X-Encryption');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
}

module.exports = {
    parseEncryptedRequest,
    sendEncryptedResponse,
    verifyAdminToken,
    requestLogger,
    errorHandler,
    validateRequiredFields,
    corsConfig
};
